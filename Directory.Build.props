<Project>
  <!-- To be redefined by GitInfo after restore. -->
  <Target Name="GitVersion" />
  
  <PropertyGroup>
    <!-- Assembly Info -->
    <Company>Ubisoft</Company>
    <Copyright>Copyright © $(Company)</Copyright>
    <Description>Sharpmake is a C#-based solution for generating project definition files, such as Visual Studio projects and solutions, GNU makefiles, Xcode projects...</Description>
    <Product>Sharpmake</Product>
    <Authors>$(Company), Sharpmake contributors</Authors>

    <!-- Custom Variables -->
    <!-- https://github.com/dotnet/sourcelink/blob/main/docs/README.md#continuousintegrationbuild -->
    <ContinuousIntegrationBuild Condition="'$(GITLAB_CI)' == 'true' OR '$(GITHUB_ACTIONS)' == 'true'">true</ContinuousIntegrationBuild>

    <!-- Compile Options -->
    <LangVersion>10.0</LangVersion>
    <TargetFramework>net6.0</TargetFramework>
    <Deterministic>true</Deterministic>
    <Features>strict</Features>
    <TreatWarningsAsErrors Condition="$(ContinuousIntegrationBuild) == 'true' OR '$(Configuration)' == 'Release'">true</TreatWarningsAsErrors>
    <EnforceCodeStyleInBuild Condition="$(ContinuousIntegrationBuild) == 'true' OR '$(Configuration)' == 'Release'">true</EnforceCodeStyleInBuild>
    <NoWarn>1570,1591</NoWarn> <!-- For xml documentation warnings -->
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>portable</DebugType>
    <InvariantGlobalization>true</InvariantGlobalization>

    <!-- Package info -->
    <PackageLicenseFile>LICENSE.md</PackageLicenseFile>
    <PackageReadmeFile>README.md</PackageReadmeFile>
  </PropertyGroup>

  <!-- Automatic versionning based on Git repository -->
  <ItemGroup>
    <PackageReference Include="GitInfo" Version="3.3.5" PrivateAssets="all" />
  </ItemGroup>

  <PropertyGroup>
    <!-- Use GitInfo only to expose Git information as msbuild Properties, we then take care of defining Versions' properties -->
    <GitVersion>false</GitVersion>
    <GitThisAssembly>false</GitThisAssembly>
    <!-- Uncomment GitInfoReportImportance to print all GitInfo information -->
    <!--<GitInfoReportImportance>high</GitInfoReportImportance>-->
  </PropertyGroup>

  <Target Name="PopulateVersionInfo" DependsOnTargets="GitVersion" BeforeTargets="GetAssemblyVersion;GenerateNuspec;GetPackageContents">
    <PropertyGroup>
      <SharpmakeDefaultVersion>0.0.1-noversion</SharpmakeDefaultVersion>
      <Version>$(GitTag)</Version> <!-- GitTag have additional information when we are not exactly on a tag, like 'git describe' --> 
      <Version Condition="$(Version) == ''">$(SharpmakeDefaultVersion)</Version>
      <PackageVersion>$(Version)</PackageVersion>
      <RepositoryBranch Condition="$(GitRoot) != ''">$(GitBranch)</RepositoryBranch>
      <RepositoryCommit Condition="$(GitRoot) != ''">$(GitSha)</RepositoryCommit>
      <SourceRevisionId Condition="$(GitRoot) != ''">$(GitBaseTag).Commits.$(GitCommits).Branch.$(GitBranch).Sha.$(GitSha)</SourceRevisionId>
    </PropertyGroup>
    
    <Message Condition="$(GitRoot) == ''" Importance="high" Text="Sharpmake Git repository can't be found" />
    <Message Condition="$(GitRoot) == ''" Importance="high" Text="Sharpmake is expected to be built inside its Git repository to automatically deduce its version. Version will default to '$(SharpmakeDefaultVersion)'" />
    
    <Message Condition="$(GitTag) == '' and $(GitRoot) != ''" Importance="high" Text="No tag found in the Git repository, version will default to '$(SharpmakeDefaultVersion)'" />
  </Target>

</Project>
