﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <PlatformTarget Condition=" '$(Platform)' == '' ">x86</PlatformTarget>
    <ProjectGuid>{1CC6264B-284A-CC77-18FC-6FCF78DC9E4E}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TestCSharpConsole</RootNamespace>
    <AssemblyName>TestCSharpConsole</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>output\vs2019\v4_7_2\debug</OutputPath>
    <IntermediateOutputPath>temp\testcsharpconsole\vs2019\v4_7_2\debug</IntermediateOutputPath>
    <DefineConstants>DEBUG;TRACE;WIN32</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>output\vs2019\v4_7_2\release</OutputPath>
    <IntermediateOutputPath>temp\testcsharpconsole\vs2019\v4_7_2\release</IntermediateOutputPath>
    <DefineConstants>TRACE;WIN32</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\codebase\TestCSharpConsole\Program.cs">
      <Link>Program.cs</Link>
    </Compile>
    <Compile Include="..\codebase\TestCSharpConsole\Properties\AssemblyInfo.cs">
      <Link>Properties\AssemblyInfo.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="clrcppproj.vs2019.v4_7_2.vcxproj">
      <Project>{3eb6f23b-6af0-875b-6f8e-d1449d6b0280}</Project>
      <Name>CLRCPPProj</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
</Project>