﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <PlatformTarget Condition=" '$(Platform)' == '' ">AnyCPU</PlatformTarget>
    <ProjectGuid>{4C9F6EEF-7F4D-6517-2359-603E7998C883}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CSharpVsix</RootNamespace>
    <AssemblyName>CSharpVsix</AssemblyName>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>..\..\codebase\csharpvsix\key.snk</AssemblyOriginatorKeyFile>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{82B43B9B-A64C-4715-B499-D71E9CA2BD60};{60DC8134-EBA5-43B8-BCC9-BB4BC16C2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <MinimumVisualStudioVersion>17.0</MinimumVisualStudioVersion>
    <OldToolsVersion>17.0</OldToolsVersion>
    <UseCodebase>true</UseCodebase>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <IncludeDebugSymbolsInVSIXContainer>True</IncludeDebugSymbolsInVSIXContainer>
    <IncludeDebugSymbolsInLocalVSIXDeployment>True</IncludeDebugSymbolsInLocalVSIXDeployment>
    <VsixType>v3</VsixType>
    <AutoGenerateBindingRedirects>True</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>output\anycpu\debug</OutputPath>
    <IntermediateOutputPath>obj\anycpu\debug</IntermediateOutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <CreateVsixContainer>True</CreateVsixContainer>
    <DeployExtension>False</DeployExtension>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>output\anycpu\release</OutputPath>
    <IntermediateOutputPath>obj\anycpu\release</IntermediateOutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <CreateVsixContainer>True</CreateVsixContainer>
    <DeployExtension>False</DeployExtension>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System.Design">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\..\codebase\CSharpVsix\HelloWorldCommand.cs">
      <Link>HelloWorldCommand.cs</Link>
    </Compile>
    <Compile Include="..\..\codebase\CSharpVsix\HelloWorldCommandPackage.cs">
      <Link>HelloWorldCommandPackage.cs</Link>
    </Compile>
    <Compile Include="..\..\codebase\CSharpVsix\Properties\AssemblyInfo.cs">
      <Link>Properties\AssemblyInfo.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <VSCTCompile Include="..\..\codebase\CSharpVsix\HelloWorldCommandPackage.vsct">
      <ResourceName>Menus.ctmenu</ResourceName>
      <SubType>Designer</SubType>
    </VSCTCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\codebase\CSharpVsix\Key.snk">
      <Link>Key.snk</Link>
    </None>
    <None Include="..\..\codebase\CSharpVsix\source.extension.vsixmanifest">
      <Link>Resources\source.extension.vsixmanifest</Link>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="..\..\codebase\CSharpVsix\Resources\HelloWorldCommand.png">
      <Link>Resources\HelloWorldCommand.png</Link>
    </Resource>
    <Resource Include="..\..\codebase\CSharpVsix\Resources\HelloWorldCommandPackage.ico">
      <Link>Resources\HelloWorldCommandPackage.ico</Link>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="..\..\codebase\CSharpVsix\VSPackage.resx">
      <SubType>Designer</SubType>
      <Link>Resources\VSPackage.resx</Link>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="..\..\codebase\CSharpVsix\Resources\HelloWorldCommand.png">
      <Link>Resources\HelloWorldCommand.png</Link>
    </Content>
    <Content Include="..\..\codebase\CSharpVsix\Resources\HelloWorldCommandPackage.ico">
      <Link>Resources\HelloWorldCommandPackage.ico</Link>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.SDK" Version="17.5.33428.388" />
  </ItemGroup>
  <PropertyGroup>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <UseCodebase>true</UseCodebase>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\VSSDK\Microsoft.VsSDK.targets" Condition="'$(VSToolsPath)' != ''" />
</Project>