﻿<Project>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <PlatformTarget Condition=" '$(Platform)' == '' ">AnyCPU</PlatformTarget>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HelloWorldLib</RootNamespace>
    <AssemblyName>HelloWorldLib</AssemblyName>
    <TargetFrameworks>net472;net6.0</TargetFrameworks>
    <FileAlignment>512</FileAlignment>
    <EnableDefaultItems>false</EnableDefaultItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(TargetFramework)'=='Debug|AnyCPU|net472'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\temp\bin\anycpu_debug_v4_7_2</OutputPath>
    <IntermediateOutputPath>..\temp\obj\anycpu_debug\helloworldlib_v4_7_2</IntermediateOutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <WarningLevel>5</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(TargetFramework)'=='Debug|AnyCPU|net6.0'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\temp\bin\anycpu_debug_net6_0</OutputPath>
    <IntermediateOutputPath>..\temp\obj\anycpu_debug\helloworldlib_net6_0</IntermediateOutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <WarningLevel>5</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(TargetFramework)'=='Release|AnyCPU|net472'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\temp\bin\anycpu_release_v4_7_2</OutputPath>
    <IntermediateOutputPath>..\temp\obj\anycpu_release\helloworldlib_v4_7_2</IntermediateOutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <WarningLevel>5</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(TargetFramework)'=='Release|AnyCPU|net6.0'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\temp\bin\anycpu_release_net6_0</OutputPath>
    <IntermediateOutputPath>..\temp\obj\anycpu_release\helloworldlib_net6_0</IntermediateOutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <WarningLevel>5</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup Condition="'$(TargetFramework)'=='net472'">
    <Reference Include="System">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="HelloWorldWriter.cs" />
  </ItemGroup>
  <ItemGroup Condition="'$(TargetFramework)'=='net6.0'">
    <PackageReference Include="System.Text.Encoding.CodePages" Version="4.5.0" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
</Project>