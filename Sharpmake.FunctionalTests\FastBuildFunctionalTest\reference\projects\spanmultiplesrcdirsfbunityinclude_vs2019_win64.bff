
//=================================================================================================================
// SpanMultipleSrcDirsFBUnityInclude FASTBuild config file
//=================================================================================================================
#once


Unity( 'SpanMultipleSrcDirsFBUnityInclude_unity' )
{
    .UnityInputFiles                    = {
                                              '.\$_CURRENT_BFF_DIR_$\..\codebase\SpanMultipleSrcDirs\additional_dir\extra_class1.cpp',
                                              '.\$_CURRENT_BFF_DIR_$\..\codebase\SpanMultipleSrcDirs\additional_dir\extra_class2.cpp',
                                              '.\$_CURRENT_BFF_DIR_$\..\codebase\SpanMultipleSrcDirs\additional_dir\extra_file.cpp',
                                              '.\$_CURRENT_BFF_DIR_$\..\codebase\SpanMultipleSrcDirs\dir_individual_files\floating_class.cpp',
                                              '.\$_CURRENT_BFF_DIR_$\..\codebase\SpanMultipleSrcDirs\dir_individual_files\floating_file.cpp',
                                              '.\$_CURRENT_BFF_DIR_$\..\codebase\SpanMultipleSrcDirs\main_dir\main.cpp',
                                              '.\$_CURRENT_BFF_DIR_$\..\codebase\SpanMultipleSrcDirs\main_dir\util.cpp'
                                          }
    .UnityInputIsolateWritableFiles     =  true
    .UnityInputIsolateWritableFilesLimit = 10
    .UnityOutputPath                    = '.\$_CURRENT_BFF_DIR_$\unity\SpanMultipleSrcDirsFBUnityInclude'
    .UnityOutputPattern                 = 'spanmultiplesrcdirsfbunityinclude_unity*.cpp'
    .UnityNumFiles                      =  1
}

////////////////////////////////////////////////////////////////////////////////
// PLATFORM SPECIFIC SECTION
#if WIN64

//=================================================================================================================
ObjectList( 'SpanMultipleSrcDirsFBUnityInclude_Debug_FastBuild_vs2019_win64_objects' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude\'

    .CompilerExtraOptions   = ''
            + ' /I".\$_CURRENT_BFF_DIR_$\..\codebase\spanmultiplesrcdirs\additional_dir"'
            + ' /I".\$_CURRENT_BFF_DIR_$\..\codebase\spanmultiplesrcdirs\dir_individual_files"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\include"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\winrt"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt"'
            + ' /Zi'
            + ' /nologo'
            + ' /W4'
            + ' /WX-'
            + ' /D"WIN64"'
            + ' /D"_CONSOLE"'
            + ' /D"_DEBUG"'
            + ' /GF'
            + ' /MTd'
            + ' /GS'
            + ' /Gy-'
            + ' /fp:fast'
            + ' /fp:except-'
            + ' /Zc:wchar_t'
            + ' /Zc:forScope'
            + ' /Zc:inline'
            + ' /GR-'
            + ' /openmp-'
            + ' /Fd".\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude\spanmultiplesrcdirsfbunityinclude_compiler.pdb"'
            + ' /Gd'
            + ' /TP'
            + ' /errorReport:queue'
            + ' /D"_MBCS"'
            + ' /FS /Zc:__cplusplus'

    .CompilerOptimizations = ''
            + ' /Od'
            + ' /Ob1'
            + ' /Oi'
            + ' /Oy-'

    // Compiler options
    // ----------------
    .CompilerOptions        = '"%1" /Fo"%2" /c'
                            + ' $CompilerExtraOptions$'
                            + ' $CompilerOptimizations$'

    .CompilerInputUnity       = 'SpanMultipleSrcDirsFBUnityInclude_unity'
    .CompilerOutputPath       = '$Intermediate$'

}


//=================================================================================================================
Executable( 'SpanMultipleSrcDirsFBUnityInclude_Debug_FastBuild_vs2019_win64_Executable' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude\'
    .Libraries              = 'SpanMultipleSrcDirsFBUnityInclude_Debug_FastBuild_vs2019_win64_objects'
    .LinkerOutput           = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude.exe'
    .LinkerLinkObjects      = false

    .LinkerOptions          = '/OUT:"%2"'
                            // Input files
                            // ---------------------------
                            + ' "%1"'
                            // General
                            // ---------------------------
                            + ' /INCREMENTAL:NO'
                            + ' /NOLOGO'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\ucrt\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\um\x64"'
                            // Input
                            // ---------------------------
                            + ' "kernel32.lib"'
                            + ' "user32.lib"'
                            + ' "gdi32.lib"'
                            + ' "winspool.lib"'
                            + ' "comdlg32.lib"'
                            + ' "advapi32.lib"'
                            + ' "shell32.lib"'
                            + ' "ole32.lib"'
                            + ' "oleaut32.lib"'
                            + ' "uuid.lib"'
                            + ' "odbc32.lib"'
                            + ' "odbccp32.lib"'
                            // Manifest
                            // ---------------------------
                            + ' /MANIFEST /MANIFESTUAC:"level=^'asInvoker^' uiAccess=^'false^'"'
                            + ' /ManifestFile:".\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude\spanmultiplesrcdirsfbunityinclude.intermediate.manifest"'
                            // Debugging
                            // ---------------------------
                            + ' /DEBUG'
                            + ' /PDB:".\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude.pdb"'
                            + ' /MAP":.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude.map"'
                            // System
                            // ---------------------------
                            + ' /SUBSYSTEM:CONSOLE'
                            + ' /LARGEADDRESSAWARE'
                            // Optimization
                            // ---------------------------
                            + ' /OPT:NOREF'
                            + ' /OPT:NOICF'
                            // Embedded IDL
                            // ---------------------------
                            + ' /TLBID:1'
                            // Windows Metadata
                            // ---------------------------
                            // Advanced
                            // ---------------------------
                            + ' /DYNAMICBASE'
                            + ' /MACHINE:X64'
                            + ' /errorReport:queue'
                            // Additional linker options
                            //--------------------------
}


//=================================================================================================================
Alias( 'SpanMultipleSrcDirsFBUnityInclude_Debug_FastBuild_vs2019_win64' )
{
    .Targets = 'SpanMultipleSrcDirsFBUnityInclude_Debug_FastBuild_vs2019_win64_Executable'
}


//=================================================================================================================
Alias( 'SpanMultipleSrcDirsFBUnityInclude_Debug_FastBuild_vs2019_win64_LibraryDependency' )
{
    .Targets = 'SpanMultipleSrcDirsFBUnityInclude_Debug_FastBuild_vs2019_win64_Executable'
}


//=================================================================================================================
ObjectList( 'SpanMultipleSrcDirsFBUnityInclude_Release_FastBuild_vs2019_win64_objects' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude\'

    .CompilerExtraOptions   = ''
            + ' /I".\$_CURRENT_BFF_DIR_$\..\codebase\spanmultiplesrcdirs\additional_dir"'
            + ' /I".\$_CURRENT_BFF_DIR_$\..\codebase\spanmultiplesrcdirs\dir_individual_files"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\include"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\winrt"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt"'
            + ' /Zi'
            + ' /nologo'
            + ' /W4'
            + ' /WX-'
            + ' /D"NDEBUG"'
            + ' /D"WIN64"'
            + ' /D"_CONSOLE"'
            + ' /GF'
            + ' /MT'
            + ' /GS-'
            + ' /Gy'
            + ' /fp:fast'
            + ' /fp:except-'
            + ' /Zc:wchar_t'
            + ' /Zc:forScope'
            + ' /Zc:inline'
            + ' /GR-'
            + ' /openmp-'
            + ' /Fd".\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude\spanmultiplesrcdirsfbunityinclude_compiler.pdb"'
            + ' /Gd'
            + ' /TP'
            + ' /errorReport:queue'
            + ' /D"_MBCS"'
            + ' /FS /Zc:__cplusplus'

    .CompilerOptimizations = ''
            + ' /Ox'
            + ' /Ob2'
            + ' /Oi'
            + ' /Ot'
            + ' /Oy-'

    // Compiler options
    // ----------------
    .CompilerOptions        = '"%1" /Fo"%2" /c'
                            + ' $CompilerExtraOptions$'
                            + ' $CompilerOptimizations$'

    .CompilerInputUnity       = 'SpanMultipleSrcDirsFBUnityInclude_unity'
    .CompilerOutputPath       = '$Intermediate$'

}


//=================================================================================================================
Executable( 'SpanMultipleSrcDirsFBUnityInclude_Release_FastBuild_vs2019_win64_Executable' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude\'
    .Libraries              = 'SpanMultipleSrcDirsFBUnityInclude_Release_FastBuild_vs2019_win64_objects'
    .LinkerOutput           = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude.exe'
    .LinkerLinkObjects      = false

    .LinkerOptions          = '/OUT:"%2"'
                            // Input files
                            // ---------------------------
                            + ' "%1"'
                            // General
                            // ---------------------------
                            + ' /INCREMENTAL:NO'
                            + ' /NOLOGO'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\ucrt\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\um\x64"'
                            // Input
                            // ---------------------------
                            + ' "kernel32.lib"'
                            + ' "user32.lib"'
                            + ' "gdi32.lib"'
                            + ' "winspool.lib"'
                            + ' "comdlg32.lib"'
                            + ' "advapi32.lib"'
                            + ' "shell32.lib"'
                            + ' "ole32.lib"'
                            + ' "oleaut32.lib"'
                            + ' "uuid.lib"'
                            + ' "odbc32.lib"'
                            + ' "odbccp32.lib"'
                            // Manifest
                            // ---------------------------
                            + ' /MANIFEST /MANIFESTUAC:"level=^'asInvoker^' uiAccess=^'false^'"'
                            + ' /ManifestFile:".\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude\spanmultiplesrcdirsfbunityinclude.intermediate.manifest"'
                            // Debugging
                            // ---------------------------
                            + ' /DEBUG'
                            + ' /PDB:".\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude.pdb"'
                            + ' /MAP":.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\spanmultiplesrcdirsfbunityinclude.map"'
                            // System
                            // ---------------------------
                            + ' /SUBSYSTEM:CONSOLE'
                            + ' /LARGEADDRESSAWARE'
                            // Optimization
                            // ---------------------------
                            + ' /OPT:REF'
                            + ' /OPT:ICF'
                            // Embedded IDL
                            // ---------------------------
                            + ' /TLBID:1'
                            // Windows Metadata
                            // ---------------------------
                            // Advanced
                            // ---------------------------
                            + ' /DYNAMICBASE'
                            + ' /MACHINE:X64'
                            + ' /errorReport:queue'
                            // Additional linker options
                            //--------------------------
}


//=================================================================================================================
Alias( 'SpanMultipleSrcDirsFBUnityInclude_Release_FastBuild_vs2019_win64' )
{
    .Targets = 'SpanMultipleSrcDirsFBUnityInclude_Release_FastBuild_vs2019_win64_Executable'
}


//=================================================================================================================
Alias( 'SpanMultipleSrcDirsFBUnityInclude_Release_FastBuild_vs2019_win64_LibraryDependency' )
{
    .Targets = 'SpanMultipleSrcDirsFBUnityInclude_Release_FastBuild_vs2019_win64_Executable'
}


#endif // WIN64
////////////////////////////////////////////////////////////////////////////////
