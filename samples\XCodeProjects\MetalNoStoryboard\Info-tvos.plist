<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleSpokenName</key>
	<string>Sharpmake Sample Metal Without Storyboard</string>
	<key>CFBundleIdentifier</key>
	<string>com.ubisoft.sharpmake.sample.metalnostoryboard.tvos</string>
	<key>CFBundleVersion</key>
	<string>1.0.0</string>
	<key>CFBundleShortVersionString</key>
	<string>1</string>
	<key>NSHumanReadableCopyright</key>
	<string>CC0</string>
	<key>LSRequiresNativeExecution</key>
	<true/>
	<key>MinimumOSVersion</key>
	<string>16</string>
	<key>NSHighResolutionCapable</key>
	<true/>
	<key>NSPrefersDisplaySafeAreaCompatibilityMode</key>
	<true/>
	<key>NSSupportsAutomaticGraphicsSwitching</key>
	<true/>
	<key>UIAppSupportsHDR</key>
	<true/>
	<key>UIInterfaceOrientation</key>
	<string>UIInterfaceOrientationLandscapeLeft</string>
	<key>UISupportsTrueScreenSizeOnMac</key>
	<true/>
</dict>
</plist>
