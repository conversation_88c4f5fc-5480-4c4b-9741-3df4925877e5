@echo off
echo Running Sharpmake HelloWorld example...
cd /d "C:\Users\<USER>\Desktop\workspace\git\Sharpmake"
echo Current directory: %CD%
echo.
echo Running Sharpmake...
"Sharpmake.Application\bin\Release\net6.0\Sharpmake.Application.exe" "/sources(samples\HelloWorld\HelloWorld.sharpmake.cs)" /verbose
echo.
echo Checking for generated files...
if exist "samples\HelloWorld\projects" (
    echo Projects folder created successfully!
    dir "samples\HelloWorld\projects" /b
) else (
    echo Projects folder not found.
)
echo.
echo Done.
pause
