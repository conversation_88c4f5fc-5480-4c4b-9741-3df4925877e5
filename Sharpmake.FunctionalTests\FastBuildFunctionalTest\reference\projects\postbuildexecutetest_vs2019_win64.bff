
//=================================================================================================================
// PostBuildExecuteTest FASTBuild config file
//=================================================================================================================
#once


Unity( 'PostBuildExecuteTest_FastBuildUnitys_unity' )
{
    .UnityInputPath                     = '.\$_CURRENT_BFF_DIR_$\..\codebase\PostBuildExecuteTest'
    .UnityInputIsolateWritableFiles     =  true
    .UnityInputIsolateWritableFilesLimit = 10
    .UnityOutputPath                    = '.\$_CURRENT_BFF_DIR_$\unity\PostBuildExecuteTest'
    .UnityOutputPattern                 = 'postbuildexecutetest_fastbuildunitys_unity*.cpp'
    .UnityNumFiles                      =  1
}

////////////////////////////////////////////////////////////////////////////////
// PLATFORM SPECIFIC SECTION
#if WIN64

//=================================================================================================================
ObjectList( 'PostBuildExecuteTest_Debug_FastBuild_NoBlob_vs2019_win64_objects' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_noblob_vs2019\postbuildexecutetest\'

    .CompilerExtraOptions   = ''
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\include"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\winrt"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt"'
            + ' /Zi'
            + ' /nologo'
            + ' /W4'
            + ' /WX-'
            + ' /D"WIN64"'
            + ' /D"_CONSOLE"'
            + ' /D"_DEBUG"'
            + ' /GF'
            + ' /MTd'
            + ' /GS'
            + ' /Gy-'
            + ' /fp:fast'
            + ' /fp:except-'
            + ' /Zc:wchar_t'
            + ' /Zc:forScope'
            + ' /Zc:inline'
            + ' /GR-'
            + ' /openmp-'
            + ' /Fd".\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_noblob_vs2019\postbuildexecutetest\postbuildexecutetest_compiler.pdb"'
            + ' /Gd'
            + ' /TP'
            + ' /errorReport:queue'
            + ' /D"_MBCS"'
            + ' /FS /Zc:__cplusplus'

    .CompilerOptimizations = ''
            + ' /Od'
            + ' /Ob1'
            + ' /Oi'
            + ' /Oy-'

    // Compiler options
    // ----------------
    .CompilerOptions        = '"%1" /Fo"%2" /c'
                            + ' $CompilerExtraOptions$'
                            + ' $CompilerOptimizations$'

    .CompilerOutputPath       = '$Intermediate$'
    .CompilerInputFiles       = '.\$_CURRENT_BFF_DIR_$\..\codebase\PostBuildExecuteTest\main.cpp'

}


//=================================================================================================================
Executable( 'PostBuildExecuteTest_Debug_FastBuild_NoBlob_vs2019_win64_Executable' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_noblob_vs2019\postbuildexecutetest\'
    .Libraries              = 'PostBuildExecuteTest_Debug_FastBuild_NoBlob_vs2019_win64_objects'
    .LinkerOutput           = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\postbuildexecutetest.exe'
    .LinkerLinkObjects      = false

    .LinkerOptions          = '/OUT:"%2"'
                            // Input files
                            // ---------------------------
                            + ' "%1"'
                            // General
                            // ---------------------------
                            + ' /INCREMENTAL:NO'
                            + ' /NOLOGO'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\ucrt\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\um\x64"'
                            // Input
                            // ---------------------------
                            + ' "kernel32.lib"'
                            + ' "user32.lib"'
                            + ' "gdi32.lib"'
                            + ' "winspool.lib"'
                            + ' "comdlg32.lib"'
                            + ' "advapi32.lib"'
                            + ' "shell32.lib"'
                            + ' "ole32.lib"'
                            + ' "oleaut32.lib"'
                            + ' "uuid.lib"'
                            + ' "odbc32.lib"'
                            + ' "odbccp32.lib"'
                            // Manifest
                            // ---------------------------
                            + ' /MANIFEST /MANIFESTUAC:"level=^'asInvoker^' uiAccess=^'false^'"'
                            + ' /ManifestFile:".\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_noblob_vs2019\postbuildexecutetest\postbuildexecutetest.intermediate.manifest"'
                            // Debugging
                            // ---------------------------
                            + ' /DEBUG'
                            + ' /PDB:".\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\postbuildexecutetest.pdb"'
                            + ' /MAP":.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\postbuildexecutetest.map"'
                            // System
                            // ---------------------------
                            + ' /SUBSYSTEM:CONSOLE'
                            + ' /LARGEADDRESSAWARE'
                            // Optimization
                            // ---------------------------
                            + ' /OPT:NOREF'
                            + ' /OPT:NOICF'
                            // Embedded IDL
                            // ---------------------------
                            + ' /TLBID:1'
                            // Windows Metadata
                            // ---------------------------
                            // Advanced
                            // ---------------------------
                            + ' /DYNAMICBASE'
                            + ' /MACHINE:X64'
                            + ' /errorReport:queue'
                            // Additional linker options
                            //--------------------------
}


//=================================================================================================================
Exec( 'Exec_postbuildexecutetest_exe_AEC27703' )
{
  .ExecExecutable       = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\postbuildexecutetest.exe'
  .ExecOutput           = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\postbuild_exec_sentinel.txt'
  .ExecUseStdOutAsOutput = true
  .PreBuildDependencies = 'PostBuildExecuteTest_Debug_FastBuild_NoBlob_vs2019_win64_Executable'
}


//=================================================================================================================
Alias( 'PostBuildExecuteTest_Debug_FastBuild_NoBlob_vs2019_win64' )
{
    .Targets = {
                   'PostBuildExecuteTest_Debug_FastBuild_NoBlob_vs2019_win64_Executable',
                   'Exec_postbuildexecutetest_exe_AEC27703'
               }
}


//=================================================================================================================
Alias( 'PostBuildExecuteTest_Debug_FastBuild_NoBlob_vs2019_win64_LibraryDependency' )
{
    .Targets = 'PostBuildExecuteTest_Debug_FastBuild_NoBlob_vs2019_win64_Executable'
}


//=================================================================================================================
ObjectList( 'PostBuildExecuteTest_Release_FastBuild_NoBlob_vs2019_win64_objects' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\release_fastbuild_noblob_vs2019\postbuildexecutetest\'

    .CompilerExtraOptions   = ''
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\include"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\winrt"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt"'
            + ' /Zi'
            + ' /nologo'
            + ' /W4'
            + ' /WX-'
            + ' /D"NDEBUG"'
            + ' /D"WIN64"'
            + ' /D"_CONSOLE"'
            + ' /GF'
            + ' /MT'
            + ' /GS-'
            + ' /Gy'
            + ' /fp:fast'
            + ' /fp:except-'
            + ' /Zc:wchar_t'
            + ' /Zc:forScope'
            + ' /Zc:inline'
            + ' /GR-'
            + ' /openmp-'
            + ' /Fd".\$_CURRENT_BFF_DIR_$\build\release_fastbuild_noblob_vs2019\postbuildexecutetest\postbuildexecutetest_compiler.pdb"'
            + ' /Gd'
            + ' /TP'
            + ' /errorReport:queue'
            + ' /D"_MBCS"'
            + ' /FS /Zc:__cplusplus'

    .CompilerOptimizations = ''
            + ' /Ox'
            + ' /Ob2'
            + ' /Oi'
            + ' /Ot'
            + ' /Oy-'

    // Compiler options
    // ----------------
    .CompilerOptions        = '"%1" /Fo"%2" /c'
                            + ' $CompilerExtraOptions$'
                            + ' $CompilerOptimizations$'

    .CompilerOutputPath       = '$Intermediate$'
    .CompilerInputFiles       = '.\$_CURRENT_BFF_DIR_$\..\codebase\PostBuildExecuteTest\main.cpp'

}


//=================================================================================================================
Executable( 'PostBuildExecuteTest_Release_FastBuild_NoBlob_vs2019_win64_Executable' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\release_fastbuild_noblob_vs2019\postbuildexecutetest\'
    .Libraries              = 'PostBuildExecuteTest_Release_FastBuild_NoBlob_vs2019_win64_objects'
    .LinkerOutput           = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\postbuildexecutetest.exe'
    .LinkerLinkObjects      = false

    .LinkerOptions          = '/OUT:"%2"'
                            // Input files
                            // ---------------------------
                            + ' "%1"'
                            // General
                            // ---------------------------
                            + ' /INCREMENTAL:NO'
                            + ' /NOLOGO'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\ucrt\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\um\x64"'
                            // Input
                            // ---------------------------
                            + ' "kernel32.lib"'
                            + ' "user32.lib"'
                            + ' "gdi32.lib"'
                            + ' "winspool.lib"'
                            + ' "comdlg32.lib"'
                            + ' "advapi32.lib"'
                            + ' "shell32.lib"'
                            + ' "ole32.lib"'
                            + ' "oleaut32.lib"'
                            + ' "uuid.lib"'
                            + ' "odbc32.lib"'
                            + ' "odbccp32.lib"'
                            // Manifest
                            // ---------------------------
                            + ' /MANIFEST /MANIFESTUAC:"level=^'asInvoker^' uiAccess=^'false^'"'
                            + ' /ManifestFile:".\$_CURRENT_BFF_DIR_$\build\release_fastbuild_noblob_vs2019\postbuildexecutetest\postbuildexecutetest.intermediate.manifest"'
                            // Debugging
                            // ---------------------------
                            + ' /DEBUG'
                            + ' /PDB:".\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\postbuildexecutetest.pdb"'
                            + ' /MAP":.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\postbuildexecutetest.map"'
                            // System
                            // ---------------------------
                            + ' /SUBSYSTEM:CONSOLE'
                            + ' /LARGEADDRESSAWARE'
                            // Optimization
                            // ---------------------------
                            + ' /OPT:REF'
                            + ' /OPT:ICF'
                            // Embedded IDL
                            // ---------------------------
                            + ' /TLBID:1'
                            // Windows Metadata
                            // ---------------------------
                            // Advanced
                            // ---------------------------
                            + ' /DYNAMICBASE'
                            + ' /MACHINE:X64'
                            + ' /errorReport:queue'
                            // Additional linker options
                            //--------------------------
}


//=================================================================================================================
Exec( 'Exec_postbuildexecutetest_exe_EC4C6497' )
{
  .ExecExecutable       = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\postbuildexecutetest.exe'
  .ExecOutput           = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\postbuild_exec_sentinel.txt'
  .ExecUseStdOutAsOutput = true
  .PreBuildDependencies = 'PostBuildExecuteTest_Release_FastBuild_NoBlob_vs2019_win64_Executable'
}


//=================================================================================================================
Alias( 'PostBuildExecuteTest_Release_FastBuild_NoBlob_vs2019_win64' )
{
    .Targets = {
                   'PostBuildExecuteTest_Release_FastBuild_NoBlob_vs2019_win64_Executable',
                   'Exec_postbuildexecutetest_exe_EC4C6497'
               }
}


//=================================================================================================================
Alias( 'PostBuildExecuteTest_Release_FastBuild_NoBlob_vs2019_win64_LibraryDependency' )
{
    .Targets = 'PostBuildExecuteTest_Release_FastBuild_NoBlob_vs2019_win64_Executable'
}


//=================================================================================================================
ObjectList( 'PostBuildExecuteTest_Debug_FastBuild_vs2019_win64_objects' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\postbuildexecutetest\'

    .CompilerExtraOptions   = ''
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\include"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\winrt"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt"'
            + ' /Zi'
            + ' /nologo'
            + ' /W4'
            + ' /WX-'
            + ' /D"WIN64"'
            + ' /D"_CONSOLE"'
            + ' /D"_DEBUG"'
            + ' /GF'
            + ' /MTd'
            + ' /GS'
            + ' /Gy-'
            + ' /fp:fast'
            + ' /fp:except-'
            + ' /Zc:wchar_t'
            + ' /Zc:forScope'
            + ' /Zc:inline'
            + ' /GR-'
            + ' /openmp-'
            + ' /Fd".\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\postbuildexecutetest\postbuildexecutetest_compiler.pdb"'
            + ' /Gd'
            + ' /TP'
            + ' /errorReport:queue'
            + ' /D"_MBCS"'
            + ' /FS /Zc:__cplusplus'

    .CompilerOptimizations = ''
            + ' /Od'
            + ' /Ob1'
            + ' /Oi'
            + ' /Oy-'

    // Compiler options
    // ----------------
    .CompilerOptions        = '"%1" /Fo"%2" /c'
                            + ' $CompilerExtraOptions$'
                            + ' $CompilerOptimizations$'

    .CompilerInputUnity       = 'PostBuildExecuteTest_FastBuildUnitys_unity'
    .CompilerOutputPath       = '$Intermediate$'

}


//=================================================================================================================
Executable( 'PostBuildExecuteTest_Debug_FastBuild_vs2019_win64_Executable' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\postbuildexecutetest\'
    .Libraries              = 'PostBuildExecuteTest_Debug_FastBuild_vs2019_win64_objects'
    .LinkerOutput           = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\postbuildexecutetest.exe'
    .LinkerLinkObjects      = false

    .LinkerOptions          = '/OUT:"%2"'
                            // Input files
                            // ---------------------------
                            + ' "%1"'
                            // General
                            // ---------------------------
                            + ' /INCREMENTAL:NO'
                            + ' /NOLOGO'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\ucrt\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\um\x64"'
                            // Input
                            // ---------------------------
                            + ' "kernel32.lib"'
                            + ' "user32.lib"'
                            + ' "gdi32.lib"'
                            + ' "winspool.lib"'
                            + ' "comdlg32.lib"'
                            + ' "advapi32.lib"'
                            + ' "shell32.lib"'
                            + ' "ole32.lib"'
                            + ' "oleaut32.lib"'
                            + ' "uuid.lib"'
                            + ' "odbc32.lib"'
                            + ' "odbccp32.lib"'
                            // Manifest
                            // ---------------------------
                            + ' /MANIFEST /MANIFESTUAC:"level=^'asInvoker^' uiAccess=^'false^'"'
                            + ' /ManifestFile:".\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\postbuildexecutetest\postbuildexecutetest.intermediate.manifest"'
                            // Debugging
                            // ---------------------------
                            + ' /DEBUG'
                            + ' /PDB:".\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\postbuildexecutetest.pdb"'
                            + ' /MAP":.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\postbuildexecutetest.map"'
                            // System
                            // ---------------------------
                            + ' /SUBSYSTEM:CONSOLE'
                            + ' /LARGEADDRESSAWARE'
                            // Optimization
                            // ---------------------------
                            + ' /OPT:NOREF'
                            + ' /OPT:NOICF'
                            // Embedded IDL
                            // ---------------------------
                            + ' /TLBID:1'
                            // Windows Metadata
                            // ---------------------------
                            // Advanced
                            // ---------------------------
                            + ' /DYNAMICBASE'
                            + ' /MACHINE:X64'
                            + ' /errorReport:queue'
                            // Additional linker options
                            //--------------------------
}


//=================================================================================================================
Exec( 'Exec_postbuildexecutetest_exe_85D9F0A6' )
{
  .ExecExecutable       = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\postbuildexecutetest.exe'
  .ExecOutput           = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\postbuild_exec_sentinel.txt'
  .ExecUseStdOutAsOutput = true
  .PreBuildDependencies = 'PostBuildExecuteTest_Debug_FastBuild_vs2019_win64_Executable'
}


//=================================================================================================================
Alias( 'PostBuildExecuteTest_Debug_FastBuild_vs2019_win64' )
{
    .Targets = {
                   'PostBuildExecuteTest_Debug_FastBuild_vs2019_win64_Executable',
                   'Exec_postbuildexecutetest_exe_85D9F0A6'
               }
}


//=================================================================================================================
Alias( 'PostBuildExecuteTest_Debug_FastBuild_vs2019_win64_LibraryDependency' )
{
    .Targets = 'PostBuildExecuteTest_Debug_FastBuild_vs2019_win64_Executable'
}


//=================================================================================================================
ObjectList( 'PostBuildExecuteTest_Release_FastBuild_vs2019_win64_objects' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\postbuildexecutetest\'

    .CompilerExtraOptions   = ''
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\include"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\winrt"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt"'
            + ' /Zi'
            + ' /nologo'
            + ' /W4'
            + ' /WX-'
            + ' /D"NDEBUG"'
            + ' /D"WIN64"'
            + ' /D"_CONSOLE"'
            + ' /GF'
            + ' /MT'
            + ' /GS-'
            + ' /Gy'
            + ' /fp:fast'
            + ' /fp:except-'
            + ' /Zc:wchar_t'
            + ' /Zc:forScope'
            + ' /Zc:inline'
            + ' /GR-'
            + ' /openmp-'
            + ' /Fd".\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\postbuildexecutetest\postbuildexecutetest_compiler.pdb"'
            + ' /Gd'
            + ' /TP'
            + ' /errorReport:queue'
            + ' /D"_MBCS"'
            + ' /FS /Zc:__cplusplus'

    .CompilerOptimizations = ''
            + ' /Ox'
            + ' /Ob2'
            + ' /Oi'
            + ' /Ot'
            + ' /Oy-'

    // Compiler options
    // ----------------
    .CompilerOptions        = '"%1" /Fo"%2" /c'
                            + ' $CompilerExtraOptions$'
                            + ' $CompilerOptimizations$'

    .CompilerInputUnity       = 'PostBuildExecuteTest_FastBuildUnitys_unity'
    .CompilerOutputPath       = '$Intermediate$'

}


//=================================================================================================================
Executable( 'PostBuildExecuteTest_Release_FastBuild_vs2019_win64_Executable' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\postbuildexecutetest\'
    .Libraries              = 'PostBuildExecuteTest_Release_FastBuild_vs2019_win64_objects'
    .LinkerOutput           = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\postbuildexecutetest.exe'
    .LinkerLinkObjects      = false

    .LinkerOptions          = '/OUT:"%2"'
                            // Input files
                            // ---------------------------
                            + ' "%1"'
                            // General
                            // ---------------------------
                            + ' /INCREMENTAL:NO'
                            + ' /NOLOGO'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\ucrt\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\um\x64"'
                            // Input
                            // ---------------------------
                            + ' "kernel32.lib"'
                            + ' "user32.lib"'
                            + ' "gdi32.lib"'
                            + ' "winspool.lib"'
                            + ' "comdlg32.lib"'
                            + ' "advapi32.lib"'
                            + ' "shell32.lib"'
                            + ' "ole32.lib"'
                            + ' "oleaut32.lib"'
                            + ' "uuid.lib"'
                            + ' "odbc32.lib"'
                            + ' "odbccp32.lib"'
                            // Manifest
                            // ---------------------------
                            + ' /MANIFEST /MANIFESTUAC:"level=^'asInvoker^' uiAccess=^'false^'"'
                            + ' /ManifestFile:".\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\postbuildexecutetest\postbuildexecutetest.intermediate.manifest"'
                            // Debugging
                            // ---------------------------
                            + ' /DEBUG'
                            + ' /PDB:".\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\postbuildexecutetest.pdb"'
                            + ' /MAP":.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\postbuildexecutetest.map"'
                            // System
                            // ---------------------------
                            + ' /SUBSYSTEM:CONSOLE'
                            + ' /LARGEADDRESSAWARE'
                            // Optimization
                            // ---------------------------
                            + ' /OPT:REF'
                            + ' /OPT:ICF'
                            // Embedded IDL
                            // ---------------------------
                            + ' /TLBID:1'
                            // Windows Metadata
                            // ---------------------------
                            // Advanced
                            // ---------------------------
                            + ' /DYNAMICBASE'
                            + ' /MACHINE:X64'
                            + ' /errorReport:queue'
                            // Additional linker options
                            //--------------------------
}


//=================================================================================================================
Exec( 'Exec_postbuildexecutetest_exe_5F9069BC' )
{
  .ExecExecutable       = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\postbuildexecutetest.exe'
  .ExecOutput           = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\postbuild_exec_sentinel.txt'
  .ExecUseStdOutAsOutput = true
  .PreBuildDependencies = 'PostBuildExecuteTest_Release_FastBuild_vs2019_win64_Executable'
}


//=================================================================================================================
Alias( 'PostBuildExecuteTest_Release_FastBuild_vs2019_win64' )
{
    .Targets = {
                   'PostBuildExecuteTest_Release_FastBuild_vs2019_win64_Executable',
                   'Exec_postbuildexecutetest_exe_5F9069BC'
               }
}


//=================================================================================================================
Alias( 'PostBuildExecuteTest_Release_FastBuild_vs2019_win64_LibraryDependency' )
{
    .Targets = 'PostBuildExecuteTest_Release_FastBuild_vs2019_win64_Executable'
}


#endif // WIN64
////////////////////////////////////////////////////////////////////////////////
