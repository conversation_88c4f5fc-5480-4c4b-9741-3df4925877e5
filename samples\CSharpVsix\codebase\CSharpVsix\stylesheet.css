body {
  margin: 0;
  padding: 0;
  border: 0;
  color: #1E1E1E;
  font-size: 13px;
  font-family: "Segoe UI", Helvetica, Arial, sans-serif;
  line-height: 1.45;
  word-wrap: break-word;
}

/* General & 'Reset' Stuff */


.container {
  width: 980px;
  margin: 0 auto;
}

section {
  display: block;
  margin: 0;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
}

/* Header, <header>
   header   - container
   h1       - project name
   h2       - project description
*/

#header {
  color: #FFF;
  background: #68217a;
  position:relative;
}
#hangcloud {
    width: 190px;
    height: 160px;
    background: url("../images/bannerart03.png");
    position: absolute;
    top: 0;
    right: -30px;
}
h1, h2 {
  font-family: "Segoe UI Light", "Segoe UI", Helvetica, Arial, sans-serif;
  line-height: 1;
  margin: 0 18px;
  padding: 0;
}
#header h1 {
  font-size: 3.4em;
  padding-top: 18px;
  font-weight: normal;
  margin-left: 15px;
}

#header h2 {
  font-size: 1.5em;
  margin-top: 10px;
  padding-bottom: 18px;
  font-weight: normal;
}


#main_content {
  width: 100%;
  display: flex;
  flex-direction: row;
}


h1, h2, h3, h4, h5, h6 {
  font-weight: bolder;
}

#main_content h1 {
  font-size: 1.8em;
  margin-top: 34px;
}

    #main_content h1:first-child {
        margin-top: 30px;
    }

#main_content h2 {
  font-size: 1.4em;
  font-weight: bold;
}
p, ul {
    margin: 11px 18px;
}

#main_content a {
    color: #06C;
    text-decoration: none;
}
ul {
        margin-top: 13px;
    margin-left: 18px;
    padding-left: 0;
}
    ul li {
        margin-left: 18px;
        padding-left: 0;
    }
#lpanel {
    width: 620px;
    float: left;
}
#rpanel ul {
    list-style-type: none;
    width: 300px;
}
    #rpanel ul li {
        line-height: 1.8em;
    }
#rpanel {
    background: #e7e7e7;
    width: 360px;
        float: right;
}

#rpanel div {
  width: 300px;
}
