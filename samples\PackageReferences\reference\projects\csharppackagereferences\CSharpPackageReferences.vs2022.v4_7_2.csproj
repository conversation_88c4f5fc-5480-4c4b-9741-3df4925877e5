﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <PlatformTarget Condition=" '$(Platform)' == '' ">x64</PlatformTarget>
    <ProjectGuid>{D52ABF58-4704-A2AD-C6A0-EB589E58C9A5}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CSharpPackageReferences</RootNamespace>
    <AssemblyName>PackageReference</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>output\vs2022\debug</OutputPath>
    <IntermediateOutputPath>obj\win64\debug</IntermediateOutputPath>
    <DefineConstants>DEBUG;TRACE;WIN64</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>NU1901,NU1902,NU1903,NU1904</WarningsNotAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>output\vs2022\release</OutputPath>
    <IntermediateOutputPath>obj\win64\release</IntermediateOutputPath>
    <DefineConstants>TRACE;WIN64</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>NU1901,NU1902,NU1903,NU1904</WarningsNotAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\..\codebase\CSharpPackageReferences\Program.cs">
      <Link>Program.cs</Link>
    </Compile>
    <Compile Include="..\..\codebase\CSharpPackageReferences\Properties\AssemblyInfo.cs">
      <Link>Properties\AssemblyInfo.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5">
        <IncludeAssets>analyzers</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.5">
        <ExcludeAssets>compile</ExcludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.CommandLine" Version="9.0.5">
        <IncludeAssets>analyzers</IncludeAssets>
        <ExcludeAssets>compile</ExcludeAssets>
        <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.5">
        <ExcludeAssets>compile</ExcludeAssets>
        <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions " Version="9.0.5">
        <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5">
        <IncludeAssets>analyzers</IncludeAssets>
        <ExcludeAssets>compile</ExcludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.5">
        <IncludeAssets>analyzers</IncludeAssets>
        <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Mono.Cecil" Version="0.9.6.4">
        <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="MySql.Data" Version="6.10.6">
        <PrivateAssets>compile;build</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="NUnit" Version="3.6.0" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
</Project>