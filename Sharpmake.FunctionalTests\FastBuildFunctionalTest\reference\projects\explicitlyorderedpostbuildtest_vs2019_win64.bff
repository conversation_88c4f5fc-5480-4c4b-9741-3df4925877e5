
//=================================================================================================================
// ExplicitlyOrderedPostBuildTest FASTBuild config file
//=================================================================================================================
#once


Unity( 'ExplicitlyOrderedPostBuildTest_FastBuildUnitys_unity' )
{
    .UnityInputPath                     = '.\$_CURRENT_BFF_DIR_$\..\codebase\ExplicitlyOrderedPostBuildTest'
    .UnityInputIsolateWritableFiles     =  true
    .UnityInputIsolateWritableFilesLimit = 10
    .UnityOutputPath                    = '.\$_CURRENT_BFF_DIR_$\unity\ExplicitlyOrderedPostBuildTest'
    .UnityOutputPattern                 = 'explicitlyorderedpostbuildtest_fastbuildunitys_unity*.cpp'
    .UnityNumFiles                      =  1
}

////////////////////////////////////////////////////////////////////////////////
// PLATFORM SPECIFIC SECTION
#if WIN64

//=================================================================================================================
ObjectList( 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_NoBlob_vs2019_win64_objects' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest\'

    .CompilerExtraOptions   = ''
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\include"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\winrt"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt"'
            + ' /Zi'
            + ' /nologo'
            + ' /W4'
            + ' /WX-'
            + ' /D"WIN64"'
            + ' /D"_CONSOLE"'
            + ' /D"_DEBUG"'
            + ' /GF'
            + ' /MTd'
            + ' /GS'
            + ' /Gy-'
            + ' /fp:fast'
            + ' /fp:except-'
            + ' /Zc:wchar_t'
            + ' /Zc:forScope'
            + ' /Zc:inline'
            + ' /GR-'
            + ' /openmp-'
            + ' /Fd".\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest\explicitlyorderedpostbuildtest_compiler.pdb"'
            + ' /Gd'
            + ' /TP'
            + ' /errorReport:queue'
            + ' /D"_MBCS"'
            + ' /FS /Zc:__cplusplus'

    .CompilerOptimizations = ''
            + ' /Od'
            + ' /Ob1'
            + ' /Oi'
            + ' /Oy-'

    // Compiler options
    // ----------------
    .CompilerOptions        = '"%1" /Fo"%2" /c'
                            + ' $CompilerExtraOptions$'
                            + ' $CompilerOptimizations$'

    .CompilerOutputPath       = '$Intermediate$'
    .CompilerInputFiles       = '.\$_CURRENT_BFF_DIR_$\..\codebase\ExplicitlyOrderedPostBuildTest\main.cpp'

}


//=================================================================================================================
Executable( 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_NoBlob_vs2019_win64_Executable' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest\'
    .Libraries              = 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_NoBlob_vs2019_win64_objects'
    .LinkerOutput           = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest.exe'
    .LinkerLinkObjects      = false

    .LinkerOptions          = '/OUT:"%2"'
                            // Input files
                            // ---------------------------
                            + ' "%1"'
                            // General
                            // ---------------------------
                            + ' /INCREMENTAL:NO'
                            + ' /NOLOGO'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\ucrt\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\um\x64"'
                            // Input
                            // ---------------------------
                            + ' "kernel32.lib"'
                            + ' "user32.lib"'
                            + ' "gdi32.lib"'
                            + ' "winspool.lib"'
                            + ' "comdlg32.lib"'
                            + ' "advapi32.lib"'
                            + ' "shell32.lib"'
                            + ' "ole32.lib"'
                            + ' "oleaut32.lib"'
                            + ' "uuid.lib"'
                            + ' "odbc32.lib"'
                            + ' "odbccp32.lib"'
                            // Manifest
                            // ---------------------------
                            + ' /MANIFEST /MANIFESTUAC:"level=^'asInvoker^' uiAccess=^'false^'"'
                            + ' /ManifestFile:".\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest\explicitlyorderedpostbuildtest.intermediate.manifest"'
                            // Debugging
                            // ---------------------------
                            + ' /DEBUG'
                            + ' /PDB:".\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest.pdb"'
                            + ' /MAP":.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest.map"'
                            // System
                            // ---------------------------
                            + ' /SUBSYSTEM:CONSOLE'
                            + ' /LARGEADDRESSAWARE'
                            // Optimization
                            // ---------------------------
                            + ' /OPT:NOREF'
                            + ' /OPT:NOICF'
                            // Embedded IDL
                            // ---------------------------
                            + ' /TLBID:1'
                            // Windows Metadata
                            // ---------------------------
                            // Advanced
                            // ---------------------------
                            + ' /DYNAMICBASE'
                            + ' /MACHINE:X64'
                            + ' /errorReport:queue'
                            // Additional linker options
                            //--------------------------
}


//=================================================================================================================
Copy( 'Copy_7C29BD29' )
{
    .Source = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest.exe'
    .Dest = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\explicitly_ordered_postbuild_test\temp_copy\explicitlyorderedpostbuildtest.exe'
    .PreBuildDependencies = 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_NoBlob_vs2019_win64_Executable'
}


//=================================================================================================================
Copy( 'Copy_1BC3C9ED' )
{
    .Source = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest.pdb'
    .Dest = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\explicitly_ordered_postbuild_test\temp_copy\explicitlyorderedpostbuildtest.pdb'
    .PreBuildDependencies = 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_NoBlob_vs2019_win64_Executable'
}


//=================================================================================================================
CopyDir( 'Copy_D8B93282' )
{
    .SourcePaths                        = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\explicitly_ordered_postbuild_test\temp_copy\'
    .SourcePathsPattern                 = '*.*'
    .SourcePathsRecurse                 = true
    .Dest                               = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_noblob_vs2019\file_copy_destination\'
    .PreBuildDependencies               = {
                                              'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_NoBlob_vs2019_win64_Executable',
                                              'Copy_7C29BD29',
                                              'Copy_1BC3C9ED'
                                          }
}

//=================================================================================================================
Alias( 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_NoBlob_vs2019_win64' )
{
    .Targets = {
                   'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_NoBlob_vs2019_win64_Executable',
                   'Copy_7C29BD29',
                   'Copy_1BC3C9ED',
                   'Copy_D8B93282'
               }
}


//=================================================================================================================
Alias( 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_NoBlob_vs2019_win64_LibraryDependency' )
{
    .Targets = 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_NoBlob_vs2019_win64_Executable'
}


//=================================================================================================================
ObjectList( 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_NoBlob_vs2019_win64_objects' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\release_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest\'

    .CompilerExtraOptions   = ''
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\include"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\winrt"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt"'
            + ' /Zi'
            + ' /nologo'
            + ' /W4'
            + ' /WX-'
            + ' /D"NDEBUG"'
            + ' /D"WIN64"'
            + ' /D"_CONSOLE"'
            + ' /GF'
            + ' /MT'
            + ' /GS-'
            + ' /Gy'
            + ' /fp:fast'
            + ' /fp:except-'
            + ' /Zc:wchar_t'
            + ' /Zc:forScope'
            + ' /Zc:inline'
            + ' /GR-'
            + ' /openmp-'
            + ' /Fd".\$_CURRENT_BFF_DIR_$\build\release_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest\explicitlyorderedpostbuildtest_compiler.pdb"'
            + ' /Gd'
            + ' /TP'
            + ' /errorReport:queue'
            + ' /D"_MBCS"'
            + ' /FS /Zc:__cplusplus'

    .CompilerOptimizations = ''
            + ' /Ox'
            + ' /Ob2'
            + ' /Oi'
            + ' /Ot'
            + ' /Oy-'

    // Compiler options
    // ----------------
    .CompilerOptions        = '"%1" /Fo"%2" /c'
                            + ' $CompilerExtraOptions$'
                            + ' $CompilerOptimizations$'

    .CompilerOutputPath       = '$Intermediate$'
    .CompilerInputFiles       = '.\$_CURRENT_BFF_DIR_$\..\codebase\ExplicitlyOrderedPostBuildTest\main.cpp'

}


//=================================================================================================================
Executable( 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_NoBlob_vs2019_win64_Executable' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\release_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest\'
    .Libraries              = 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_NoBlob_vs2019_win64_objects'
    .LinkerOutput           = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest.exe'
    .LinkerLinkObjects      = false

    .LinkerOptions          = '/OUT:"%2"'
                            // Input files
                            // ---------------------------
                            + ' "%1"'
                            // General
                            // ---------------------------
                            + ' /INCREMENTAL:NO'
                            + ' /NOLOGO'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\ucrt\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\um\x64"'
                            // Input
                            // ---------------------------
                            + ' "kernel32.lib"'
                            + ' "user32.lib"'
                            + ' "gdi32.lib"'
                            + ' "winspool.lib"'
                            + ' "comdlg32.lib"'
                            + ' "advapi32.lib"'
                            + ' "shell32.lib"'
                            + ' "ole32.lib"'
                            + ' "oleaut32.lib"'
                            + ' "uuid.lib"'
                            + ' "odbc32.lib"'
                            + ' "odbccp32.lib"'
                            // Manifest
                            // ---------------------------
                            + ' /MANIFEST /MANIFESTUAC:"level=^'asInvoker^' uiAccess=^'false^'"'
                            + ' /ManifestFile:".\$_CURRENT_BFF_DIR_$\build\release_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest\explicitlyorderedpostbuildtest.intermediate.manifest"'
                            // Debugging
                            // ---------------------------
                            + ' /DEBUG'
                            + ' /PDB:".\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest.pdb"'
                            + ' /MAP":.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest.map"'
                            // System
                            // ---------------------------
                            + ' /SUBSYSTEM:CONSOLE'
                            + ' /LARGEADDRESSAWARE'
                            // Optimization
                            // ---------------------------
                            + ' /OPT:REF'
                            + ' /OPT:ICF'
                            // Embedded IDL
                            // ---------------------------
                            + ' /TLBID:1'
                            // Windows Metadata
                            // ---------------------------
                            // Advanced
                            // ---------------------------
                            + ' /DYNAMICBASE'
                            + ' /MACHINE:X64'
                            + ' /errorReport:queue'
                            // Additional linker options
                            //--------------------------
}


//=================================================================================================================
Copy( 'Copy_31D92349' )
{
    .Source = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest.exe'
    .Dest = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\explicitly_ordered_postbuild_test\temp_copy\explicitlyorderedpostbuildtest.exe'
    .PreBuildDependencies = 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_NoBlob_vs2019_win64_Executable'
}


//=================================================================================================================
Copy( 'Copy_4528720D' )
{
    .Source = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\explicitlyorderedpostbuildtest.pdb'
    .Dest = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\explicitly_ordered_postbuild_test\temp_copy\explicitlyorderedpostbuildtest.pdb'
    .PreBuildDependencies = 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_NoBlob_vs2019_win64_Executable'
}


//=================================================================================================================
CopyDir( 'Copy_6DDD5222' )
{
    .SourcePaths                        = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\explicitly_ordered_postbuild_test\temp_copy\'
    .SourcePathsPattern                 = '*.*'
    .SourcePathsRecurse                 = true
    .Dest                               = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_noblob_vs2019\file_copy_destination\'
    .PreBuildDependencies               = {
                                              'ExplicitlyOrderedPostBuildTest_Release_FastBuild_NoBlob_vs2019_win64_Executable',
                                              'Copy_31D92349',
                                              'Copy_4528720D'
                                          }
}

//=================================================================================================================
Alias( 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_NoBlob_vs2019_win64' )
{
    .Targets = {
                   'ExplicitlyOrderedPostBuildTest_Release_FastBuild_NoBlob_vs2019_win64_Executable',
                   'Copy_31D92349',
                   'Copy_4528720D',
                   'Copy_6DDD5222'
               }
}


//=================================================================================================================
Alias( 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_NoBlob_vs2019_win64_LibraryDependency' )
{
    .Targets = 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_NoBlob_vs2019_win64_Executable'
}


//=================================================================================================================
ObjectList( 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_vs2019_win64_objects' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\explicitlyorderedpostbuildtest\'

    .CompilerExtraOptions   = ''
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\include"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\winrt"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt"'
            + ' /Zi'
            + ' /nologo'
            + ' /W4'
            + ' /WX-'
            + ' /D"WIN64"'
            + ' /D"_CONSOLE"'
            + ' /D"_DEBUG"'
            + ' /GF'
            + ' /MTd'
            + ' /GS'
            + ' /Gy-'
            + ' /fp:fast'
            + ' /fp:except-'
            + ' /Zc:wchar_t'
            + ' /Zc:forScope'
            + ' /Zc:inline'
            + ' /GR-'
            + ' /openmp-'
            + ' /Fd".\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\explicitlyorderedpostbuildtest\explicitlyorderedpostbuildtest_compiler.pdb"'
            + ' /Gd'
            + ' /TP'
            + ' /errorReport:queue'
            + ' /D"_MBCS"'
            + ' /FS /Zc:__cplusplus'

    .CompilerOptimizations = ''
            + ' /Od'
            + ' /Ob1'
            + ' /Oi'
            + ' /Oy-'

    // Compiler options
    // ----------------
    .CompilerOptions        = '"%1" /Fo"%2" /c'
                            + ' $CompilerExtraOptions$'
                            + ' $CompilerOptimizations$'

    .CompilerInputUnity       = 'ExplicitlyOrderedPostBuildTest_FastBuildUnitys_unity'
    .CompilerOutputPath       = '$Intermediate$'

}


//=================================================================================================================
Executable( 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_vs2019_win64_Executable' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\explicitlyorderedpostbuildtest\'
    .Libraries              = 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_vs2019_win64_objects'
    .LinkerOutput           = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\explicitlyorderedpostbuildtest.exe'
    .LinkerLinkObjects      = false

    .LinkerOptions          = '/OUT:"%2"'
                            // Input files
                            // ---------------------------
                            + ' "%1"'
                            // General
                            // ---------------------------
                            + ' /INCREMENTAL:NO'
                            + ' /NOLOGO'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\ucrt\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\um\x64"'
                            // Input
                            // ---------------------------
                            + ' "kernel32.lib"'
                            + ' "user32.lib"'
                            + ' "gdi32.lib"'
                            + ' "winspool.lib"'
                            + ' "comdlg32.lib"'
                            + ' "advapi32.lib"'
                            + ' "shell32.lib"'
                            + ' "ole32.lib"'
                            + ' "oleaut32.lib"'
                            + ' "uuid.lib"'
                            + ' "odbc32.lib"'
                            + ' "odbccp32.lib"'
                            // Manifest
                            // ---------------------------
                            + ' /MANIFEST /MANIFESTUAC:"level=^'asInvoker^' uiAccess=^'false^'"'
                            + ' /ManifestFile:".\$_CURRENT_BFF_DIR_$\build\debug_fastbuild_vs2019\explicitlyorderedpostbuildtest\explicitlyorderedpostbuildtest.intermediate.manifest"'
                            // Debugging
                            // ---------------------------
                            + ' /DEBUG'
                            + ' /PDB:".\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\explicitlyorderedpostbuildtest.pdb"'
                            + ' /MAP":.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\explicitlyorderedpostbuildtest.map"'
                            // System
                            // ---------------------------
                            + ' /SUBSYSTEM:CONSOLE'
                            + ' /LARGEADDRESSAWARE'
                            // Optimization
                            // ---------------------------
                            + ' /OPT:NOREF'
                            + ' /OPT:NOICF'
                            // Embedded IDL
                            // ---------------------------
                            + ' /TLBID:1'
                            // Windows Metadata
                            // ---------------------------
                            // Advanced
                            // ---------------------------
                            + ' /DYNAMICBASE'
                            + ' /MACHINE:X64'
                            + ' /errorReport:queue'
                            // Additional linker options
                            //--------------------------
}


//=================================================================================================================
Copy( 'Copy_168D55D5' )
{
    .Source = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\explicitlyorderedpostbuildtest.exe'
    .Dest = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\explicitly_ordered_postbuild_test\temp_copy\explicitlyorderedpostbuildtest.exe'
    .PreBuildDependencies = 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_vs2019_win64_Executable'
}


//=================================================================================================================
Copy( 'Copy_FA09E015' )
{
    .Source = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\explicitlyorderedpostbuildtest.pdb'
    .Dest = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\explicitly_ordered_postbuild_test\temp_copy\explicitlyorderedpostbuildtest.pdb'
    .PreBuildDependencies = 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_vs2019_win64_Executable'
}


//=================================================================================================================
CopyDir( 'Copy_80ADA5CA' )
{
    .SourcePaths                        = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\explicitly_ordered_postbuild_test\temp_copy\'
    .SourcePathsPattern                 = '*.*'
    .SourcePathsRecurse                 = true
    .Dest                               = '.\$_CURRENT_BFF_DIR_$\output\debug_fastbuild_vs2019\file_copy_destination\'
    .PreBuildDependencies               = {
                                              'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_vs2019_win64_Executable',
                                              'Copy_168D55D5',
                                              'Copy_FA09E015'
                                          }
}

//=================================================================================================================
Alias( 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_vs2019_win64' )
{
    .Targets = {
                   'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_vs2019_win64_Executable',
                   'Copy_168D55D5',
                   'Copy_FA09E015',
                   'Copy_80ADA5CA'
               }
}


//=================================================================================================================
Alias( 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_vs2019_win64_LibraryDependency' )
{
    .Targets = 'ExplicitlyOrderedPostBuildTest_Debug_FastBuild_vs2019_win64_Executable'
}


//=================================================================================================================
ObjectList( 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_vs2019_win64_objects' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\explicitlyorderedpostbuildtest\'

    .CompilerExtraOptions   = ''
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include"'
            + ' /I"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\include"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\winrt"'
            + ' /I"C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt"'
            + ' /Zi'
            + ' /nologo'
            + ' /W4'
            + ' /WX-'
            + ' /D"NDEBUG"'
            + ' /D"WIN64"'
            + ' /D"_CONSOLE"'
            + ' /GF'
            + ' /MT'
            + ' /GS-'
            + ' /Gy'
            + ' /fp:fast'
            + ' /fp:except-'
            + ' /Zc:wchar_t'
            + ' /Zc:forScope'
            + ' /Zc:inline'
            + ' /GR-'
            + ' /openmp-'
            + ' /Fd".\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\explicitlyorderedpostbuildtest\explicitlyorderedpostbuildtest_compiler.pdb"'
            + ' /Gd'
            + ' /TP'
            + ' /errorReport:queue'
            + ' /D"_MBCS"'
            + ' /FS /Zc:__cplusplus'

    .CompilerOptimizations = ''
            + ' /Ox'
            + ' /Ob2'
            + ' /Oi'
            + ' /Ot'
            + ' /Oy-'

    // Compiler options
    // ----------------
    .CompilerOptions        = '"%1" /Fo"%2" /c'
                            + ' $CompilerExtraOptions$'
                            + ' $CompilerOptimizations$'

    .CompilerInputUnity       = 'ExplicitlyOrderedPostBuildTest_FastBuildUnitys_unity'
    .CompilerOutputPath       = '$Intermediate$'

}


//=================================================================================================================
Executable( 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_vs2019_win64_Executable' )
{
    Using( .win64Config )
    .Intermediate           = '.\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\explicitlyorderedpostbuildtest\'
    .Libraries              = 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_vs2019_win64_objects'
    .LinkerOutput           = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\explicitlyorderedpostbuildtest.exe'
    .LinkerLinkObjects      = false

    .LinkerOptions          = '/OUT:"%2"'
                            // Input files
                            // ---------------------------
                            + ' "%1"'
                            // General
                            // ---------------------------
                            + ' /INCREMENTAL:NO'
                            + ' /NOLOGO'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\ucrt\x64"'
                            + ' /LIBPATH:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.19041.0\um\x64"'
                            // Input
                            // ---------------------------
                            + ' "kernel32.lib"'
                            + ' "user32.lib"'
                            + ' "gdi32.lib"'
                            + ' "winspool.lib"'
                            + ' "comdlg32.lib"'
                            + ' "advapi32.lib"'
                            + ' "shell32.lib"'
                            + ' "ole32.lib"'
                            + ' "oleaut32.lib"'
                            + ' "uuid.lib"'
                            + ' "odbc32.lib"'
                            + ' "odbccp32.lib"'
                            // Manifest
                            // ---------------------------
                            + ' /MANIFEST /MANIFESTUAC:"level=^'asInvoker^' uiAccess=^'false^'"'
                            + ' /ManifestFile:".\$_CURRENT_BFF_DIR_$\build\release_fastbuild_vs2019\explicitlyorderedpostbuildtest\explicitlyorderedpostbuildtest.intermediate.manifest"'
                            // Debugging
                            // ---------------------------
                            + ' /DEBUG'
                            + ' /PDB:".\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\explicitlyorderedpostbuildtest.pdb"'
                            + ' /MAP":.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\explicitlyorderedpostbuildtest.map"'
                            // System
                            // ---------------------------
                            + ' /SUBSYSTEM:CONSOLE'
                            + ' /LARGEADDRESSAWARE'
                            // Optimization
                            // ---------------------------
                            + ' /OPT:REF'
                            + ' /OPT:ICF'
                            // Embedded IDL
                            // ---------------------------
                            + ' /TLBID:1'
                            // Windows Metadata
                            // ---------------------------
                            // Advanced
                            // ---------------------------
                            + ' /DYNAMICBASE'
                            + ' /MACHINE:X64'
                            + ' /errorReport:queue'
                            // Additional linker options
                            //--------------------------
}


//=================================================================================================================
Copy( 'Copy_CFEB2B75' )
{
    .Source = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\explicitlyorderedpostbuildtest.exe'
    .Dest = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\explicitly_ordered_postbuild_test\temp_copy\explicitlyorderedpostbuildtest.exe'
    .PreBuildDependencies = 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_vs2019_win64_Executable'
}


//=================================================================================================================
Copy( 'Copy_1A9C4EF5' )
{
    .Source = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\explicitlyorderedpostbuildtest.pdb'
    .Dest = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\explicitly_ordered_postbuild_test\temp_copy\explicitlyorderedpostbuildtest.pdb'
    .PreBuildDependencies = 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_vs2019_win64_Executable'
}


//=================================================================================================================
CopyDir( 'Copy_03C6D0EA' )
{
    .SourcePaths                        = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\explicitly_ordered_postbuild_test\temp_copy\'
    .SourcePathsPattern                 = '*.*'
    .SourcePathsRecurse                 = true
    .Dest                               = '.\$_CURRENT_BFF_DIR_$\output\release_fastbuild_vs2019\file_copy_destination\'
    .PreBuildDependencies               = {
                                              'ExplicitlyOrderedPostBuildTest_Release_FastBuild_vs2019_win64_Executable',
                                              'Copy_CFEB2B75',
                                              'Copy_1A9C4EF5'
                                          }
}

//=================================================================================================================
Alias( 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_vs2019_win64' )
{
    .Targets = {
                   'ExplicitlyOrderedPostBuildTest_Release_FastBuild_vs2019_win64_Executable',
                   'Copy_CFEB2B75',
                   'Copy_1A9C4EF5',
                   'Copy_03C6D0EA'
               }
}


//=================================================================================================================
Alias( 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_vs2019_win64_LibraryDependency' )
{
    .Targets = 'ExplicitlyOrderedPostBuildTest_Release_FastBuild_vs2019_win64_Executable'
}


#endif // WIN64
////////////////////////////////////////////////////////////////////////////////
