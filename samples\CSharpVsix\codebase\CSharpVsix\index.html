﻿<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>

    <link rel="stylesheet" type="text/css" href="stylesheet.css" media="screen">

    <title>Getting Started</title>
</head>

<body>
    <div class="container">
        <div id="header">
            <h1>Getting Started</h1>
            <h2>Visual Studio Extensions</h2>
        </div>

        <div id="main_content">
            <div id="lpanel">
                <h1>Creating a Visual Studio Extension</h1>

                <p>This project enables developers to create an extension for Visual Studio. The solution contains a VSIX project that packages the extension into a VSIX file. This file is used to install an extension for Visual Studio.</p>
                <h2>Add new features</h2>

                <ol>
                    <li>Right-click the project node in Solution Explorer and select Add&gt;New Item.</li>
                    <li>In the Add New Item dialog box, expand the Extensibility node under Visual C# or Visual Basic.</li>
                    <li>Choose from the available item templates: Visual Studio Package, Editor Items (Classifier, Margin, Text Adornment, Viewport Adornment), Command, Tool Window, Toolbox Control, and then click Add.</li>
                </ol>

                <p>The files for the template that you selected are added to the project. You can start adding functionality to your item template, press F5 to run the project, or add additional item templates.</p>

                <h2>Run and debug</h2>
                <p>To run the project, press F5. Visual Studio will:</p>

                <ul>
                    <li>Build the extension from the VSIX project.</li>
                    <li>Create a VSIX package from the VSIX project.</li>
                    <li>When debugging, start an experimental instance of Visual Studio with the VSIX package installed.</li>
                </ul>

                <p>In the experimental instance of Visual Studio you can test out the functionality of your extension without affecting your Visual Studio installation.</p>

            </div>
            <div id="rpanel">
                <div>
                    <h1>Visual Studio Extensibility Resources</h1>

                    <ol>
                        <li><a target="_blank" href="http://aka.ms/d0ru3v">MSDN documentation</a><br />Detailed documentation and API reference material for building extensions.</li>
                        <li><a target="_blank" href="http://aka.ms/pauhge">Extension samples on GitHub</a><br />Use a sample project to kickstart your development.</li>
                        <li><a target="_blank" href="http://aka.ms/l24u91">Extensibility chat room on Gitter</a><br />Meet other extension developers and exchange tips and tricks for extension development.</li>
                        <li><a target="_blank" href="http://aka.ms/fwg1ft">Channel 9 videos on extensibility</a><br />Watch videos from the product team on Visual Studio extensibility.</li>
                        <li><a target="_blank" href="http://aka.ms/ui0qn6">Extensibility Tools 2015</a><br />Install an optional helper tool that adds extra IDE support for extension authors.</li>
                    </ol>
                    <h1>Give us feedback</h1>
                    <ul>
                        <li><a target="_blank" href="http://aka.ms/uonulm">Submit a new feature idea or suggestion</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
