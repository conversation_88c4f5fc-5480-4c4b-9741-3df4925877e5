# Generated by Sharpmake -- Do not edit !
ifndef config
  config=debug
endif

ifndef verbose
  SILENT = @
endif

ifeq ($(config),debug)
  CXX        = g++
  AR         = ar
  OBJDIR     = ../../obj/linux_debug/static_lib1
  TARGETDIR  = ../../lib/linux_debug/static_lib1
  TARGET     = $(TARGETDIR)/libstatic_lib1.a
  DEFINES   += -D "_DEBUG"
  INCLUDES  += -I../../../static_lib1
  CPPFLAGS  += -MMD -MP $(DEFINES) $(INCLUDES)
  CFLAGS    += $(CPPFLAGS) -g -Wall -fPIC
  CXXFLAGS  += $(CFLAGS) -fno-exceptions -fno-rtti 
  LDFLAGS   +=  
  LDLIBS    +=  
  RESFLAGS  += $(DEFINES) $(INCLUDES)
  LDDEPS    += 
  LINKCMD    = $(AR) -rcs $(TARGET) $(OBJECTS)
  PCH        = ../../../static_lib1/src/pch.h
  PCHOUT     = $(OBJDIR)/pch.h
  GCH        = $(OBJDIR)/pch.h.gch
  PCHCMD     = -include $(PCHOUT)
  define PREBUILDCMDS
    
  endef
  define PRELINKCMDS
    
  endef
  define POSTBUILDCMDS
    
  endef
  define POSTFILECMDS
  endef
endif

ifeq ($(config),release)
  CXX        = g++
  AR         = ar
  OBJDIR     = ../../obj/linux_release/static_lib1
  TARGETDIR  = ../../lib/linux_release/static_lib1
  TARGET     = $(TARGETDIR)/libstatic_lib1.a
  DEFINES   += -D "NDEBUG"
  INCLUDES  += -I../../../static_lib1
  CPPFLAGS  += -MMD -MP $(DEFINES) $(INCLUDES)
  CFLAGS    += $(CPPFLAGS) -g -O3 -Wall -fPIC
  CXXFLAGS  += $(CFLAGS) -fno-exceptions -fno-rtti 
  LDFLAGS   +=  
  LDLIBS    +=  
  RESFLAGS  += $(DEFINES) $(INCLUDES)
  LDDEPS    += 
  LINKCMD    = $(AR) -rcs $(TARGET) $(OBJECTS)
  PCH        = ../../../static_lib1/src/pch.h
  PCHOUT     = $(OBJDIR)/pch.h
  GCH        = $(OBJDIR)/pch.h.gch
  PCHCMD     = -include $(PCHOUT)
  define PREBUILDCMDS
    
  endef
  define PRELINKCMDS
    
  endef
  define POSTBUILDCMDS
    
  endef
  define POSTFILECMDS
  endef
endif

ifeq ($(config),debug)
  OBJECTS += $(OBJDIR)/ensure_debug.o
  #OBJECTS += $(OBJDIR)/ensure_release.o
  OBJECTS += $(OBJDIR)/pch.o
  OBJECTS += $(OBJDIR)/util_static_lib1.o
endif

ifeq ($(config),release)
  #OBJECTS += $(OBJDIR)/ensure_debug.o
  OBJECTS += $(OBJDIR)/ensure_release.o
  OBJECTS += $(OBJDIR)/pch.o
  OBJECTS += $(OBJDIR)/util_static_lib1.o
endif

RESOURCES := \

SHELLTYPE := msdos
ifeq (,$(ComSpec)$(COMSPEC))
  SHELLTYPE := posix
endif
ifeq (/bin,$(findstring /bin,$(SHELL)))
  SHELLTYPE := posix
endif

.PHONY: clean prebuild prelink

all: $(TARGETDIR) $(OBJDIR) prebuild prelink $(TARGET)
	@:

$(TARGET): $(GCH) $(OBJECTS) $(LDDEPS) $(RESOURCES) | $(TARGETDIR)
	@echo Linking static_lib1
	$(SILENT) $(LINKCMD)
	$(POSTBUILDCMDS)

$(TARGETDIR):
	@echo Creating $(TARGETDIR)
ifeq (posix,$(SHELLTYPE))
	$(SILENT) mkdir -p $(TARGETDIR)
else
	$(SILENT) if not exist $(subst /,\\,$(TARGETDIR)) mkdir $(subst /,\\,$(TARGETDIR))
endif

ifneq ($(OBJDIR),$(TARGETDIR))
$(OBJDIR):
	@echo Creating $(OBJDIR)
ifeq (posix,$(SHELLTYPE))
	$(SILENT) mkdir -p $(OBJDIR)
else
	$(SILENT) if not exist $(subst /,\\,$(OBJDIR)) mkdir $(subst /,\\,$(OBJDIR))
endif
endif

clean:
	@echo Cleaning static_lib1
ifeq (posix,$(SHELLTYPE))
	$(SILENT) rm -f  $(TARGET)
	$(SILENT) rm -rf $(OBJDIR)
else
	$(SILENT) if exist $(subst /,\\,$(TARGET)) del $(subst /,\\,$(TARGET))
	$(SILENT) if exist $(subst /,\\,$(OBJDIR)) rmdir /s /q $(subst /,\\,$(OBJDIR))
endif

prebuild:
	$(PREBUILDCMDS)

prelink:
	$(PRELINKCMDS)

ifneq (,$(PCH))
$(GCH): $(PCH) | $(OBJDIR)
	@echo $(notdir $<)
	-$(SILENT) cp $< $(OBJDIR)
	$(SILENT) $(CXX) $(CXXFLAGS) -xc++-header -o "$@" -c "$<"
	$(SILENT) $(POSTFILECMDS)
endif

$(OBJDIR)/ensure_debug.o: ../../../static_lib1/src/ensure_debug.cpp $(GCH) | $(OBJDIR)
	@echo $(notdir $<)
	$(SILENT) $(CXX) $(CXXFLAGS) $(PCHCMD) -o "$@" -c "$<"
	$(SILENT) $(POSTFILECMDS)

$(OBJDIR)/ensure_release.o: ../../../static_lib1/src/ensure_release.cpp $(GCH) | $(OBJDIR)
	@echo $(notdir $<)
	$(SILENT) $(CXX) $(CXXFLAGS) $(PCHCMD) -o "$@" -c "$<"
	$(SILENT) $(POSTFILECMDS)

$(OBJDIR)/pch.o: ../../../static_lib1/src/pch.cpp $(GCH) | $(OBJDIR)
	@echo $(notdir $<)
	$(SILENT) $(CXX) $(CXXFLAGS) $(PCHCMD) -o "$@" -c "$<"
	$(SILENT) $(POSTFILECMDS)

$(OBJDIR)/util_static_lib1.o: ../../../static_lib1/src/util_static_lib1.cpp $(GCH) | $(OBJDIR)
	@echo $(notdir $<)
	$(SILENT) $(CXX) $(CXXFLAGS) $(PCHCMD) -o "$@" -c "$<"
	$(SILENT) $(POSTFILECMDS)

-include $(OBJECTS:%.o=%.d)
-include $(GCH:%.gch=%.d)
