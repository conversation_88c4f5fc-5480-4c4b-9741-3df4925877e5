﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Windows;

[assembly: AssemblyTitle("HelloWorld")]
[assembly: AssemblyDescription("Dummy description")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("Ubisoft")]
[assembly: AssemblyProduct("Sharpmake")]
[assembly: AssemblyCopyright("Copyright © Ubisoft 2020")]

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
