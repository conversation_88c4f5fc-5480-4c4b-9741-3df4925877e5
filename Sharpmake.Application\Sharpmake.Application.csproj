﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <ServerGarbageCollection>true</ServerGarbageCollection>
    <RollForward>Major</RollForward>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Sharpmake.Generators\Sharpmake.Generators.csproj" />
    <ProjectReference Include="..\Sharpmake.Platforms\Sharpmake.CommonPlatforms\Sharpmake.CommonPlatforms.csproj" />
    <ProjectReference Include="..\Sharpmake\Sharpmake.csproj" />
  </ItemGroup>

  <!-- Properties for Sharpmake "extended" setup -->
  <!-- This setup allow another solution (.sln) to reference Sharpmake core projects while at the same time adding additional platforms and extensions in the dependency chain -->
  <Choose>
    <!-- Automatically add "extended" platforms and extensions if they are presents *and* if the build is not done from the core Sharpmake.sln solution -->
    <When Condition="'$(SolutionName)' != 'Sharpmake'">
      <ItemGroup>
		<ProjectReference Condition="Exists('..\..\Sharpmake.Platforms')" Include="..\..\Sharpmake.Platforms\**\*.csproj" />
        <ProjectReference Condition="Exists('..\..\Sharpmake.Extensions')" Include="..\..\Sharpmake.Extensions\**\*.csproj" />
      </ItemGroup>
    </When>
  </Choose>

  <PropertyGroup>
    <!-- Disable Visual Studio checks done when building the project from within the IDE. -->
    <!-- These checks may skip building the project when it is used by multiple solution (.sln) while msbuild still need to cleanup the output folder. -->
    <!-- MsBuild will properly handle the skip if needed. -->
    <!-- https://github.com/dotnet/project-system/blob/main/docs/repo/up-to-date-check-implementation.md -->
    <DisableFastUpToDateCheck>True</DisableFastUpToDateCheck>
  </PropertyGroup>

  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
</Project>
