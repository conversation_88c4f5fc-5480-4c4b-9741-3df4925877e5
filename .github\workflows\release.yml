# Sharpmake GitHub Actions release workflow
name: release

on:
  push:
    tags:
      - '**'

permissions:
  contents: write

jobs:
  builds:
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-2022]
        framework: [net6.0]
        configuration: [Release]
    uses: ./.github/workflows/build.yml
    with:
      os: ${{ matrix.os }}
      framework: ${{ matrix.framework }}
      configuration: ${{ matrix.configuration }}
      run_unit_tests: false
      run_regression_tests: false

  release:
    needs: [builds]
    runs-on: 'ubuntu-latest'
    permissions:
          contents: write

    steps:
      - name: 'Checkout the repo'
        uses: actions/checkout@v4.2.2
        with:
          fetch-depth: 0

      - name: 'Generating changelog'
        shell: pwsh
        run: .github\Get-Changelog.ps1 ${{ github.ref_name }} > changelog.md

      - name: 'Download workflow artifacts'
        uses: actions/download-artifact@v4
        with:
          path: downloads

      - name: 'Create release archives'
        run: |
          for OS in Linux macOS Windows
          do
              pushd ./Sharpmake-net6.0-$OS-${{ github.sha }}
              zip -r ../Sharpmake-net6.0-$OS-${{ github.ref_name }}.zip .
              popd
          done
        working-directory: downloads

      - name: 'Create GitHub release'
        uses: ncipollo/release-action@v1.16.0
        with:
          bodyFile: 'changelog.md'
          artifacts: 'downloads/Sharpmake-net6.0-*-${{ github.ref_name }}.zip'
