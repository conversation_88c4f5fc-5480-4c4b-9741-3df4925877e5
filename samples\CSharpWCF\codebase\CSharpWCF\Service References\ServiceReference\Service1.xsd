<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/CSharpWCFApp" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/CSharpWCFApp" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="CompositeType">
    <xs:sequence>
      <xs:element minOccurs="0" name="BoolValue" type="xs:boolean" />
      <xs:element minOccurs="0" name="StringValue" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CompositeType" nillable="true" type="tns:CompositeType" />
</xs:schema>