# Generated by Sharpmake -- Do not edit !
ifndef config
  config=debug
endif

ifndef verbose
  SILENT = @
endif

ifeq ($(config),debug)
  CXX        = g++
  AR         = ar
  OBJDIR     = ../../obj/linux_debug/header-only-lib
  TARGETDIR  = ../../bin/linux_debug
  TARGET     = $(TARGETDIR)/libheader-only-lib.a
  DEFINES   += -D "_DEBUG"
  INCLUDES  += -I../../../header-only-lib
  CPPFLAGS  += -MMD -MP $(DEFINES) $(INCLUDES)
  CFLAGS    += $(CPPFLAGS) -g -Wall 
  CXXFLAGS  += $(CFLAGS) -fno-exceptions -fno-rtti 
  LDFLAGS   +=  
  LDLIBS    +=  
  RESFLAGS  += $(DEFINES) $(INCLUDES)
  LDDEPS    += 
  LINKCMD    = 
  PCH        = 
  PCHOUT     = 
  GCH        = 
  PCHCMD     = 
  define PREBUILDCMDS
    
  endef
  define PRELINKCMDS
    
  endef
  define POSTBUILDCMDS
    
  endef
  define POSTFILECMDS
  endef
endif

ifeq ($(config),release)
  CXX        = g++
  AR         = ar
  OBJDIR     = ../../obj/linux_release/header-only-lib
  TARGETDIR  = ../../bin/linux_release
  TARGET     = $(TARGETDIR)/libheader-only-lib.a
  DEFINES   += -D "NDEBUG"
  INCLUDES  += -I../../../header-only-lib
  CPPFLAGS  += -MMD -MP $(DEFINES) $(INCLUDES)
  CFLAGS    += $(CPPFLAGS) -g -O3 -Wall 
  CXXFLAGS  += $(CFLAGS) -fno-exceptions -fno-rtti 
  LDFLAGS   +=  
  LDLIBS    +=  
  RESFLAGS  += $(DEFINES) $(INCLUDES)
  LDDEPS    += 
  LINKCMD    = 
  PCH        = 
  PCHOUT     = 
  GCH        = 
  PCHCMD     = 
  define PREBUILDCMDS
    
  endef
  define PRELINKCMDS
    
  endef
  define POSTBUILDCMDS
    
  endef
  define POSTFILECMDS
  endef
endif

ifeq ($(config),debug)
endif

ifeq ($(config),release)
endif

RESOURCES := \

SHELLTYPE := msdos
ifeq (,$(ComSpec)$(COMSPEC))
  SHELLTYPE := posix
endif
ifeq (/bin,$(findstring /bin,$(SHELL)))
  SHELLTYPE := posix
endif

.PHONY: clean prebuild prelink

all: $(TARGETDIR) $(OBJDIR) prebuild prelink $(TARGET)
	@:

$(TARGET): $(GCH) $(OBJECTS) $(LDDEPS) $(RESOURCES) | $(TARGETDIR)
	@echo Linking header-only-lib
	$(SILENT) $(LINKCMD)
	$(POSTBUILDCMDS)

$(TARGETDIR):
	@echo Creating $(TARGETDIR)
ifeq (posix,$(SHELLTYPE))
	$(SILENT) mkdir -p $(TARGETDIR)
else
	$(SILENT) if not exist $(subst /,\\,$(TARGETDIR)) mkdir $(subst /,\\,$(TARGETDIR))
endif

ifneq ($(OBJDIR),$(TARGETDIR))
$(OBJDIR):
	@echo Creating $(OBJDIR)
ifeq (posix,$(SHELLTYPE))
	$(SILENT) mkdir -p $(OBJDIR)
else
	$(SILENT) if not exist $(subst /,\\,$(OBJDIR)) mkdir $(subst /,\\,$(OBJDIR))
endif
endif

clean:
	@echo Cleaning header-only-lib
ifeq (posix,$(SHELLTYPE))
	$(SILENT) rm -f  $(TARGET)
	$(SILENT) rm -rf $(OBJDIR)
else
	$(SILENT) if exist $(subst /,\\,$(TARGET)) del $(subst /,\\,$(TARGET))
	$(SILENT) if exist $(subst /,\\,$(OBJDIR)) rmdir /s /q $(subst /,\\,$(OBJDIR))
endif

prebuild:
	$(PREBUILDCMDS)

prelink:
	$(PRELINKCMDS)

ifneq (,$(PCH))
$(GCH): $(PCH) | $(OBJDIR)
	@echo $(notdir $<)
	-$(SILENT) cp $< $(OBJDIR)
	$(SILENT) $(CXX) $(CXXFLAGS) -xc++-header -o "$@" -c "$<"
	$(SILENT) $(POSTFILECMDS)
endif

-include $(OBJECTS:%.o=%.d)
-include $(GCH:%.gch=%.d)
