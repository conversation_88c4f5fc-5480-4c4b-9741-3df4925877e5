<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!-- Enable PackageReference support in C++ projects, see https://github.com/NuGet/NuGet.Client/pull/3145#issuecomment-888769783 -->
  <PropertyGroup Condition="'$(MSBuildProjectExtension)' == '.vcxproj'">
    <NuGetTargetMoniker Condition="'$(NuGetTargetMoniker)' == ''">native,Version=v0.0</NuGetTargetMoniker>
  </PropertyGroup>
  
  <ItemGroup Condition="'$(MSBuildProjectExtension)' == '.vcxproj'">
    <ProjectCapability Include="PackageReferences" />
  </ItemGroup>

  <PropertyGroup>

    <MSBuildProjectExtensionsPath>obj\$(MSBuildProjectName)\</MSBuildProjectExtensionsPath>
    
  </PropertyGroup>

</Project>
