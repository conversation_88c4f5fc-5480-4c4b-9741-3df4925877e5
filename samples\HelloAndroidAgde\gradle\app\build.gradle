apply plugin: 'com.android.application'

android {
    if (!project.hasProperty('MSBUILD_NDK_VERSION'))
        ext.MSBUILD_NDK_VERSION = NDK_VERSION
    if (!project.hasProperty('MSBUILD_MIN_SDK_VERSION'))
        ext.MSBUILD_MIN_SDK_VERSION = MIN_SDK_VERSION
    if (!project.hasProperty('MSBUILD_JNI_LIBS_SRC_DIR'))
        ext.MSBUILD_JNI_LIBS_SRC_DIR = JNI_LIBS_SRC_DIR
    if (!project.hasProperty('MSBUILD_ANDROID_OUTPUT_APK_NAME'))
        ext.MSBUILD_ANDROID_OUTPUT_APK_NAME = ANDROID_OUTPUT_APK_NAME

    def isFastBuild = 'False';
    if (project.hasProperty('FastBuild'))
        isFastBuild = FastBuild
    def libCppShared = 'False'
    if (project.hasProperty('LibCppShared'))
        libCppShared = LibCppShared
    def ndkPath  = ''
    if (project.hasProperty('ndkRoot'))
        ndkPath = ndkRoot
    def abi = ''
    if (project.hasProperty('ABI'))
        abi = ABI

    tasks.named('preBuild') {
        doFirst {
            if (isFastBuild == 'True')
            {
                delete project.buildDir
                delete file("${buildDir}/outputs/apk/Debug_FastBuild/" + ANDROID_OUTPUT_APK_NAME)

                if (libCppShared == 'True')
                {
                    def libCppSharedFile = "/libc++_shared.so"
                    def libCppSharedSrc = ndkPath + "/sources/cxx-stl/llvm-libc++/libs/" + abi + libCppSharedFile
                    def libCppSharedDst = MSBUILD_JNI_LIBS_SRC_DIR + "/" + abi + libCppSharedFile

                    if (!file(libCppSharedDst).exists())
                    {
                        copy {
                            from libCppSharedSrc
                            into MSBUILD_JNI_LIBS_SRC_DIR + "/" + abi + "/."
                        }
                    }
                }
            }
        }
    }

    compileSdkVersion 29
    ndkVersion MSBUILD_NDK_VERSION
    defaultConfig {
        applicationId "com.example.helloandroid.agde"
        minSdkVersion MSBUILD_MIN_SDK_VERSION
        targetSdkVersion 29
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        externalNativeBuild {
            cmake {
                cppFlags ""
            }
        }
    }
    signingConfigs {
        release {
            storeFile file('test-keystore.jks')
            storePassword 'testkeystore'
            keyAlias 'key0'
            keyPassword 'testkey'
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            debuggable true
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            debuggable true
        }

        Debug_FastBuild {
            initWith debug
        }
        Release_FastBuild {
            initWith release
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = [MSBUILD_JNI_LIBS_SRC_DIR]
        }
    }

    applicationVariants.all { variant ->
        variant.outputs.all {
            outputFileName = MSBUILD_ANDROID_OUTPUT_APK_NAME
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.0.2'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
}
