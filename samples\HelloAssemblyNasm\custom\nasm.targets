<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <PropertyPageSchema
      Include="$(MSBuildThisFileDirectory)$(MSBuildThisFileName).xml" />
    <AvailableItemName Include="NASM">
      <Targets>_NASM</Targets>
    </AvailableItemName>
  </ItemGroup>
  <PropertyGroup>
    <ComputeLinkInputsTargets>
      $(ComputeLinkInputsTargets);
      ComputeNASMOutput;
    </ComputeLinkInputsTargets>
    <ComputeLibInputsTargets>
      $(ComputeLibInputsTargets);
      ComputeNASMOutput;
    </ComputeLibInputsTargets>
  </PropertyGroup>
  <UsingTask
    TaskName="NASM"
    TaskFactory="XamlTaskFactory"
    AssemblyName="Microsoft.Build.Tasks.v4.0">
    <Task>$(MSBuildThisFileDirectory)$(MSBuildThisFileName).xml</Task>
  </UsingTask>
  <Target
    Name="_WriteNasmTlogs"
    Condition="'@(NASM)' != '' and '@(SelectedFiles)' == ''">
    <ItemGroup>
      <NASM Remove="@(NASM)" Condition="'%(NASM.ExcludedFromBuild)' == 'true' or '%(NASM.ObjectFileName)' == ''" />
    </ItemGroup>
    <ItemGroup Condition="'@(NASM)' != ''">
      <_NasmReadTlog Include="^%(NASM.FullPath);%(NASM.AdditionalDependencies)" />
      <_NasmWriteTlog Include="^%(NASM.FullPath);$([MSBuild]::NormalizePath('$(MSBuildProjectDirectory)', '%(NASM.ObjectFileName)'))" />
    </ItemGroup>
    <WriteLinesToFile
      Condition="'@(_NasmReadTlog)' != ''"
      File="$(TLogLocation)Nasm.read.1u.tlog"
      Lines="@(_NasmReadTlog->MetaData('Identity')->ToUpperInvariant());"
      Overwrite="true"
      Encoding="Unicode"/>
    <WriteLinesToFile
      Condition="'@(_NasmWriteTlog)' != ''"
      File="$(TLogLocation)Nasm.write.1u.tlog"
      Lines="@(_NasmWriteTlog->MetaData('Identity')->ToUpperInvariant());"
      Overwrite="true"
      Encoding="Unicode"/>
    <ItemGroup>
      <_NasmReadTlog Remove="@(_NasmReadTlog)" />
      <_NasmWriteTlog Remove="@(_NasmWriteTlog)" />
    </ItemGroup>
  </Target>
  <Target
    Name="_NASM"
    BeforeTargets="$(NASMBeforeTargets)"
    AfterTargets="$(NASMAfterTargets)"
    Condition="'@(NASM)' != ''"
    Outputs="%(NASM.ObjectFileName)"
    Inputs="%(NASM.Identity);%(NASM.AdditionalDependencies);$(MSBuildProjectFile)"
    DependsOnTargets="_WriteNasmTlogs;_SelectedFiles">
    <ItemGroup Condition="'@(SelectedFiles)' != ''">
      <NASM Remove="@(NASM)" Condition="'%(Identity)' != '@(SelectedFiles)'" />
    </ItemGroup>
    <Message
      Condition="'@(NASM)' != '' and '%(NASM.ExcludedFromBuild)' != 'true'"
      Importance="High"
      Text="%(NASM.ExecutionDescription)" />
    <NASM
      Condition="'@(NASM)' != '' and '%(NASM.ExcludedFromBuild)' != 'true'"
      Inputs="%(NASM.Inputs)"
      ObjectFileName="%(NASM.ObjectFileName)"
      SymbolsPrefix="%(NASM.SymbolsPrefix)"
      SymbolsPostfix="%(NASM.SymbolsPostfix)"
      GenerateDebugInformation="%(NASM.GenerateDebugInformation)"
      IncludePaths="%(NASM.IncludePaths)"
      PreIncludeFiles="%(NASM.PreIncludeFiles)"
      PreprocessorDefinitions="%(NASM.PreprocessorDefinitions)"
      UndefinePreprocessorDefinitions="%(NASM.UndefinePreprocessorDefinitions)"
      TreatWarningsAsErrors="%(NASM.TreatWarningsAsErrors)"
      CommandLineTemplate="%(NASM.CommandLineTemplate)"
      AdditionalOptions="%(NASM.AdditionalOptions)"
      Path="%(NASM.Path)"
    />
  </Target>
  <Target
    Name="ComputeNASMOutput"
    Condition="'@(NASM)' != ''">
    <ItemGroup>
      <Link Include="@(NASM->Metadata('ObjectFileName')->Distinct()->ClearMetadata())" Condition="'%(NASM.ExcludedFromBuild)' != 'true'"/>
      <Lib Include="@(NASM->Metadata('ObjectFileName')->Distinct()->ClearMetadata())" Condition="'%(NASM.ExcludedFromBuild)' != 'true'"/>
    </ItemGroup>
  </Target>
</Project>
