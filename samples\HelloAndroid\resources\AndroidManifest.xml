<?xml version="1.0" encoding="utf-8"?>
<!-- Changes made to Package Name should also be reflected in the Debugging - Package Name property, in the Property Pages -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
        package="com.$(ApplicationName)"
        android:versionCode="1"
        android:versionName="1.0">

    <!-- This .apk has no Java code itself, so set hasCode to false. -->
    <application android:label="$(ApplicationName)" android:hasCode="false">

        <!-- Our activity is the built-in NativeActivity framework class.
             This will take care of integrating with our NDK code. -->
        <activity android:name="android.app.NativeActivity"
                android:label="$(ApplicationName)"
                android:configChanges="orientation|keyboardHidden"
                android:screenOrientation="landscape">
            <!-- Tell NativeActivity the name of our .so -->
            <meta-data android:name="android.app.lib_name"
                    android:value="exe" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest> 
