﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <PlatformTarget Condition=" '$(Platform)' == '' ">AnyCPU</PlatformTarget>
    <ProjectGuid>{742456DD-70C9-26F7-F5DE-98386A8E8919}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CSharpWCFApp</RootNamespace>
    <AssemblyName>CSharpWCFApp</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{3D9AD99F-2412-4246-B90B-4EAA41C64699};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
  </PropertyGroup>
  <ProjectExtensions>
      <VisualStudio>
        <FlavorProperties GUID = "{3D9AD99F-2412-4246-B90B-4EAA41C64699}">
          <WcfProjectProperties>
          </WcfProjectProperties>
        </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>output\anycpu\debug</OutputPath>
    <IntermediateOutputPath>obj\anycpu\debug</IntermediateOutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>output\anycpu\release</OutputPath>
    <IntermediateOutputPath>obj\anycpu\release</IntermediateOutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp">
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Configuration">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.EnterpriseServices">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web.ApplicationServices">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web.DynamicData">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web.Entity">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web.Extensions">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web.Services">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\..\codebase\CSharpWCFApp\IService1.cs">
      <Link>IService1.cs</Link>
    </Compile>
    <Compile Include="..\..\codebase\CSharpWCFApp\Properties\AssemblyInfo.cs">
      <Link>Properties\AssemblyInfo.cs</Link>
    </Compile>
    <Compile Include="..\..\codebase\CSharpWCFApp\Service1.svc.cs">
      <Link>Service1.svc.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\codebase\CSharpWCFApp\Web.Debug.config">
      <Link>Resources\Web.Debug.config</Link>
    </None>
    <None Include="..\..\codebase\CSharpWCFApp\Web.Release.config">
      <Link>Resources\Web.Release.config</Link>
    </None>
    <None Include="..\..\codebase\CSharpWCFApp\Web.config">
      <Link>Resources\Web.config</Link>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
</Project>