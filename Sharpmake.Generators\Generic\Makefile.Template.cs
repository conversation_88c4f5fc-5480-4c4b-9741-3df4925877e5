﻿// Copyright (c) Ubisoft. All Rights Reserved.
// Licensed under the Apache 2.0 License. See LICENSE.md in the project root for license information.

namespace Sharpmake.Generators.Generic
{
    public partial class Makefile
    {
        public static class Template
        {
            private static string NewLine = System.Environment.NewLine;

            public static class Solution
            {
                public static string Header =
@"# Generated by Sharpmake -- Do not edit !
# Type ""make help"" for usage help

ifndef config
  config=[defaultConfig]
endif
export config

";
                public static string ProjectsVariableBegin = $"PROJECTS := \\{NewLine}";

                public static string ProjectsVariableElement = $"\t[projectName] \\{NewLine}";

                public static string ProjectsVariableEnd = NewLine;

                public static string PhonyTargets = $".PHONY: all clean help $(PROJECTS){NewLine}{NewLine}";

                public static string AllRule = $"all: $(PROJECTS){NewLine}{NewLine}";

                public static string ProjectRuleBegin = "[projectName]: ";

                public static string ProjectRuleDependency = "[dependencyName] ";

                public static string ProjectRuleEnd =
                    $"{NewLine}\t@echo \" ==== Building [projectName] ($(config)) ====\"" +
                    $"{NewLine}\t@${{MAKE}} --no-print-directory -C \"[projectFileDirectory]\" -f \"[projectFileName]\"{NewLine}{NewLine}";

                public static string CleanRuleBegin = $"clean:{NewLine}";

                public static string CleanRuleProject = $"\t@${{MAKE}} --no-print-directory -C \"[projectFileDirectory]\" -f \"[projectFileName]\" clean{NewLine}";

                public static string CleanRuleEnd = NewLine;

                public static string HelpRuleBegin =
                    $"help:{NewLine}" +
                    $"\t@echo \"Usage: make [config = name] [target]\"{NewLine}" +
                    $"\t@echo \"\"{NewLine}" +
                    $"\t@echo \"CONFIGURATIONS:\"{NewLine}";

                public static string HelpRuleConfiguration = $"\t@echo \"   [optimization]\"{NewLine}";

                public static string HelpRuleTargetsBegin =
                    $"\t@echo \"\"{NewLine}" +
                    $"\t@echo \"TARGETS:\"{NewLine}" +
                    $"\t@echo \"   all (default)\"{NewLine}" +
                    $"\t@echo \"   clean\"{NewLine}";

                public static string HelpRuleTarget = $"\t@echo \"   [projectName]\"{NewLine}";

                public static string HelpRuleEnd = NewLine;
            }

            public static class Project
            {
                public static string Header =
@"# Generated by Sharpmake -- Do not edit !
ifndef config
  config=debug
endif

ifndef verbose
  SILENT = @
endif

";

                public static string ProjectConfigurationVariables =
@"ifeq ($(config),[name])
  CXX        = [options.CompilerToUse]
  AR         = ar
  OBJDIR     = [options.IntermediateDirectory]
  TARGETDIR  = [options.OutputDirectory]
  TARGET     = $(TARGETDIR)/[options.OutputFile]
  DEFINES   += [options.Defines]
  INCLUDES  += [options.Includes]
  CPPFLAGS  += -MMD -MP $(DEFINES) $(INCLUDES)
  CFLAGS    += $(CPPFLAGS) [options.CFLAGS]
  CXXFLAGS  += $(CFLAGS) [options.CXXFLAGS]
  LDFLAGS   += [options.LibraryPaths] [options.AdditionalLinkerOptions]
  LDLIBS    += [options.LibsStartGroup][options.DependenciesLibraryFiles] [options.LibraryFiles][options.LibsEndGroup]
  RESFLAGS  += $(DEFINES) $(INCLUDES)
  LDDEPS    += [options.LDDEPS]
  LINKCMD    = [options.LinkCommand]
  PCH        = [precompHeader]
  PCHOUT     = [precompHeaderOut]
  GCH        = [precompIntermediate]
  PCHCMD     = [precompCommand]
  define PREBUILDCMDS
    [precompPreBuildCmds]
  endef
  define PRELINKCMDS
    [precompPreLinkCmds]
  endef
  define POSTBUILDCMDS
    [precompPostBuildCmds]
  endef
  define POSTFILECMDS
  endef
endif

";
                public static string LinkCommandLib = "$(AR) -rcs $(TARGET) $(OBJECTS)";

                public static string LinkCommandDll = "$(CXX) -shared -o $(TARGET) $(OBJECTS) $(LDFLAGS) $(RESOURCES) $(LDLIBS)";

                public static string LinkCommandExe = "$(CXX) -o $(TARGET) $(OBJECTS) $(LDFLAGS) $(RESOURCES) $(LDLIBS)";

                public static string ObjectsVariableBegin = $"ifeq ($(config),[name]){NewLine}";

                public static string ObjectsVariableElement = $"  [excludeChar]OBJECTS += $(OBJDIR)/[objectFile]{NewLine}";

                public static string ObjectsVariableEnd = $"endif{NewLine}{NewLine}";

                public static string ProjectRulesGeneral =
@"RESOURCES := \

SHELLTYPE := msdos
ifeq (,$(ComSpec)$(COMSPEC))
  SHELLTYPE := posix
endif
ifeq (/bin,$(findstring /bin,$(SHELL)))
  SHELLTYPE := posix
endif

.PHONY: clean prebuild prelink

all: $(TARGETDIR) $(OBJDIR) prebuild prelink $(TARGET)
	@:

$(TARGET): $(GCH) $(OBJECTS) $(LDDEPS) $(RESOURCES) | $(TARGETDIR)
	@echo Linking [projectName]
	$(SILENT) $(LINKCMD)
	$(POSTBUILDCMDS)

$(TARGETDIR):
	@echo Creating $(TARGETDIR)
ifeq (posix,$(SHELLTYPE))
	$(SILENT) mkdir -p $(TARGETDIR)
else
	$(SILENT) if not exist $(subst /,\\,$(TARGETDIR)) mkdir $(subst /,\\,$(TARGETDIR))
endif

ifneq ($(OBJDIR),$(TARGETDIR))
$(OBJDIR):
	@echo Creating $(OBJDIR)
ifeq (posix,$(SHELLTYPE))
	$(SILENT) mkdir -p $(OBJDIR)
else
	$(SILENT) if not exist $(subst /,\\,$(OBJDIR)) mkdir $(subst /,\\,$(OBJDIR))
endif
endif

clean:
	@echo Cleaning [projectName]
ifeq (posix,$(SHELLTYPE))
	$(SILENT) rm -f  $(TARGET)
	$(SILENT) rm -rf $(OBJDIR)
else
	$(SILENT) if exist $(subst /,\\,$(TARGET)) del $(subst /,\\,$(TARGET))
	$(SILENT) if exist $(subst /,\\,$(OBJDIR)) rmdir /s /q $(subst /,\\,$(OBJDIR))
endif

prebuild:
	$(PREBUILDCMDS)

prelink:
	$(PRELINKCMDS)

ifneq (,$(PCH))
$(GCH): $(PCH) | $(OBJDIR)
	@echo $(notdir $<)
	-$(SILENT) cp $< $(OBJDIR)
	$(SILENT) $(CXX) $(CXXFLAGS) -xc++-header -o ""$@"" -c ""$<""
	$(SILENT) $(POSTFILECMDS)
endif

";

                public static readonly string ObjectRuleCxx =
@"$(OBJDIR)/[objectFile]: [sourceFile] $(GCH) | $(OBJDIR)
	@echo $(notdir $<)
	$(SILENT) $(CXX) $(CXXFLAGS) $(PCHCMD) -o ""$@"" -c ""$<""
	$(SILENT) $(POSTFILECMDS)

";

                public static readonly string ObjectRuleC =
@"$(OBJDIR)/[objectFile]: [sourceFile] $(GCH) | $(OBJDIR)
	@echo $(notdir $<)
	$(SILENT) $(CXX)  $(CFLAGS) $(PCHCMD) -x c -o ""$@"" -c ""$<""
	$(SILENT) $(POSTFILECMDS)

";

                public static string Footer =
@"-include $(OBJECTS:%.o=%.d)
-include $(GCH:%.gch=%.d)
";
            }
        }
    }
}
