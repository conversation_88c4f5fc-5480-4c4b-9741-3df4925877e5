# Generated by Sharpmake -- Do not edit !
# Type "make help" for usage help

ifndef config
  config=debug
endif
export config

PROJECTS := \
	dll1 \
	exe \
	header-only-lib \
	lib_group \
	static lib2 \
	static_lib1 \

.PHONY: all clean help $(PROJECTS)

all: $(PROJECTS)

dll1: static_lib1 
	@echo " ==== Building dll1 ($(config)) ===="
	@${MAKE} --no-print-directory -C "../projects/dll1" -f "dll1_Linux_make.make"

exe: dll1 header-only-lib lib_group static_lib1 static lib2 
	@echo " ==== Building exe ($(config)) ===="
	@${MAKE} --no-print-directory -C "../projects/exe" -f "exe_Linux_make.make"

header-only-lib: 
	@echo " ==== Building header-only-lib ($(config)) ===="
	@${MAKE} --no-print-directory -C "../projects/header-only-lib" -f "header-only-lib_Linux_make.make"

lib_group: dll1 static_lib1 
	@echo " ==== Building lib_group ($(config)) ===="
	@${MAKE} --no-print-directory -C "../projects/lib_group" -f "lib_group_Linux_make.make"

static lib2: 
	@echo " ==== Building static lib2 ($(config)) ===="
	@${MAKE} --no-print-directory -C "../projects/static lib2" -f "static lib2_Linux_make.make"

static_lib1: 
	@echo " ==== Building static_lib1 ($(config)) ===="
	@${MAKE} --no-print-directory -C "../projects/static_lib1" -f "static_lib1_Linux_make.make"

clean:
	@${MAKE} --no-print-directory -C "../projects/dll1" -f "dll1_Linux_make.make" clean
	@${MAKE} --no-print-directory -C "../projects/exe" -f "exe_Linux_make.make" clean
	@${MAKE} --no-print-directory -C "../projects/header-only-lib" -f "header-only-lib_Linux_make.make" clean
	@${MAKE} --no-print-directory -C "../projects/lib_group" -f "lib_group_Linux_make.make" clean
	@${MAKE} --no-print-directory -C "../projects/static lib2" -f "static lib2_Linux_make.make" clean
	@${MAKE} --no-print-directory -C "../projects/static_lib1" -f "static_lib1_Linux_make.make" clean

help:
	@echo "Usage: make [config = name] [target]"
	@echo ""
	@echo "CONFIGURATIONS:"
	@echo "   debug"
	@echo "   release"
	@echo ""
	@echo "TARGETS:"
	@echo "   all (default)"
	@echo "   clean"
	@echo "   dll1"
	@echo "   exe"
	@echo "   header-only-lib"
	@echo "   lib_group"
	@echo "   static lib2"
	@echo "   static_lib1"

