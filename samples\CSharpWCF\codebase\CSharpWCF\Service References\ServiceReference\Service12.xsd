<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:61670/Service1.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/CSharpWCFApp" />
  <xs:element name="GetData">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="value" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDataResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetDataResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDataUsingDataContract">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/CSharpWCFApp" minOccurs="0" name="composite" nillable="true" type="q1:CompositeType" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDataUsingDataContractResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/CSharpWCFApp" minOccurs="0" name="GetDataUsingDataContractResult" nillable="true" type="q2:CompositeType" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>