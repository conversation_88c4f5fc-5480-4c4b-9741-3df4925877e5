<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ID="cdc53885-bd4f-4adb-9a06-5613de1b9ca2" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <GenerateTaskBasedAsynchronousMethod>true</GenerateTaskBasedAsynchronousMethod>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>false</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings />
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <UseSerializerForFaults>true</UseSerializerForFaults>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="http://localhost:61670/Service1.svc" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="Service1.wsdl" MetadataType="Wsdl" ID="05a03a34-0b2f-4a5f-bc12-dbabf09588f2" SourceId="1" SourceUrl="http://localhost:61670/Service1.svc?wsdl" />
    <MetadataFile FileName="Service1.xsd" MetadataType="Schema" ID="7dfcb69d-0ec8-4c67-b113-b8f629815277" SourceId="1" SourceUrl="http://localhost:61670/Service1.svc?xsd=xsd2" />
    <MetadataFile FileName="Service11.xsd" MetadataType="Schema" ID="d80f62bf-7e24-4572-9c03-aed7cb65edac" SourceId="1" SourceUrl="http://localhost:61670/Service1.svc?xsd=xsd1" />
    <MetadataFile FileName="Service12.xsd" MetadataType="Schema" ID="36cec659-5b26-4b9e-a439-a239df8c7fab" SourceId="1" SourceUrl="http://localhost:61670/Service1.svc?xsd=xsd0" />
    <MetadataFile FileName="Service1.disco" MetadataType="Disco" ID="534f3bfb-59fb-42af-9689-be3955db3c74" SourceId="1" SourceUrl="http://localhost:61670/Service1.svc?disco" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration91.svcinfo" Name="configuration91.svcinfo" />
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>