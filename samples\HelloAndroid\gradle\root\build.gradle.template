apply plugin: 'base'
// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
       google()
       mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:$(GradlePlugin)'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}
