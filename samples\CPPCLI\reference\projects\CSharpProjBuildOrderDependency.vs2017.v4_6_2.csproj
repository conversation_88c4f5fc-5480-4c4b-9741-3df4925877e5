﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <PlatformTarget Condition=" '$(Platform)' == '' ">x86</PlatformTarget>
    <ProjectGuid>{CABE51EB-3AED-01E0-3DDB-A72DFBA0ECE5}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CSharpProjBuildOrderDependency</RootNamespace>
    <AssemblyName>CSharpProjBuildOrderDependency</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>output\vs2017\v4_6_2\debug</OutputPath>
    <IntermediateOutputPath>temp\csharpprojbuildorderdependency\vs2017\v4_6_2\debug</IntermediateOutputPath>
    <DefineConstants>DEBUG;TRACE;WIN32</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>output\vs2017\v4_6_2\release</OutputPath>
    <IntermediateOutputPath>temp\csharpprojbuildorderdependency\vs2017\v4_6_2\release</IntermediateOutputPath>
    <DefineConstants>TRACE;WIN32</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\codebase\CSharpProjBuildOrderDependency\Class1.cs">
      <Link>Class1.cs</Link>
    </Compile>
    <Compile Include="..\codebase\CSharpProjBuildOrderDependency\Properties\AssemblyInfo.cs">
      <Link>Properties\AssemblyInfo.cs</Link>
    </Compile>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
</Project>