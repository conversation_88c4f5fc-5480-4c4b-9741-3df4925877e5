
//=================================================================================================================
// Master FASTBuild config file
//=================================================================================================================
#once


//=================================================================================================================
// Global Settings
//=================================================================================================================
Settings
{
    #import TMP
    #import TEMP
    #import USERPROFILE
    .Environment =
    {
        "TMP=$TMP$",
        "TEMP=$TEMP$",
        "USERPROFILE=$USERPROFILE$",
        "SystemRoot=C:\WINDOWS"
        "PATH=C:\Program Files (x86)\Windows Kits\10\bin\10.0.19041.0\x64"
        "KEY=VALUE"
    }

}

//=================================================================================================================
Compiler( 'Compiler-x64-vs2019' )
{
    .ExecutableRootPath     = 'C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\HostX64\x64'
    .Executable             = '$ExecutableRootPath$\cl.exe'
    .ExtraFiles             = {
                                '$ExecutableRootPath$\1033\clui.dll',
                                '$ExecutableRootPath$\c1.dll',
                                '$ExecutableRootPath$\c1xx.dll',
                                '$ExecutableRootPath$\c2.dll',
                                '$ExecutableRootPath$\msobj140.dll',
                                '$ExecutableRootPath$\mspdb140.dll',
                                '$ExecutableRootPath$\mspdbcore.dll',
                                '$ExecutableRootPath$\mspdbsrv.exe',
                                '$ExecutableRootPath$\mspft140.dll',
                                '$ExecutableRootPath$\msvcdis140.dll',
                                '$ExecutableRootPath$\msvcp140.dll',
                                '$ExecutableRootPath$\msvcp140_atomic_wait.dll',
                                '$ExecutableRootPath$\pgodb140.dll',
                                '$ExecutableRootPath$\tbbmalloc.dll',
                                '$ExecutableRootPath$\vcruntime140.dll',
                                '$ExecutableRootPath$\vcruntime140_1.dll',
                                'C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-console-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-console-l1-2-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-datetime-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-debug-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-errorhandling-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-file-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-file-l1-2-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-file-l2-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-handle-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-heap-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-interlocked-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-libraryloader-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-localization-l1-2-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-memory-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-namedpipe-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-processenvironment-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-processthreads-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-processthreads-l1-1-1.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-profile-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-rtlsupport-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-string-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-synch-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-synch-l1-2-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-sysinfo-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-timezone-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-core-util-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-conio-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-convert-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-environment-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-filesystem-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-heap-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-locale-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-math-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-multibyte-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-private-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-process-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-runtime-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-stdio-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-string-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-time-l1-1-0.dll',
                                'C:\Program Files (x86)\Windows Kits\10\Redist\10.0.19041.0\ucrt\DLLs\x64\api-ms-win-crt-utility-l1-1-0.dll'
                            }
    .CompilerFamily         = 'msvc'
}

Compiler( 'RC.win64Config' )
{
    .Executable             = 'C:\Program Files (x86)\Windows Kits\10\bin\10.0.19041.0\x64\rc.exe'
    .CompilerFamily         = 'custom'
}

.win64Config =
[
    .BinPath                = 'C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64'
    .LinkerPath             = 'C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64'
    .ResourceCompiler       = 'RC.win64Config'
    .Compiler               = 'Compiler-x64-vs2019'
    .Librarian              = '$LinkerPath$\lib.exe'
    .Linker                 = '$LinkerPath$\link.exe'
]

Compiler( 'ML.win64ConfigMasm' )
{
    .Executable             = 'C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\ml64.exe'
    .CompilerFamily         = 'custom'
}

.win64ConfigMasm =
[
    Using( .win64Config )
    .Compiler               = 'ML.win64ConfigMasm'
]
