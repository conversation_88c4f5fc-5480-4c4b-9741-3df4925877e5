﻿<Project>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <PlatformTarget Condition=" '$(Platform)' == '' ">AnyCPU</PlatformTarget>
    <OutputType>Exe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HelloWorldExe</RootNamespace>
    <AssemblyName>HelloWorldExe</AssemblyName>
    <TargetFrameworks>net472;net6.0;net6.0-windows</TargetFrameworks>
    <FileAlignment>512</FileAlignment>
    <EnableDefaultItems>false</EnableDefaultItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(TargetFramework)'=='Debug|AnyCPU|net472'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\temp\bin\anycpu_debug_v4_7_2</OutputPath>
    <IntermediateOutputPath>..\temp\obj\anycpu_debug\helloworldexe_v4_7_2</IntermediateOutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <WarningLevel>5</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(TargetFramework)'=='Debug|AnyCPU|net6.0'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\temp\bin\anycpu_debug_net6_0</OutputPath>
    <IntermediateOutputPath>..\temp\obj\anycpu_debug\helloworldexe_net6_0</IntermediateOutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <WarningLevel>5</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(TargetFramework)'=='Debug|AnyCPU|net6.0-windows'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\temp\bin\anycpu_debug_net6_0_windows</OutputPath>
    <IntermediateOutputPath>..\temp\obj\anycpu_debug\helloworldexe_net6_0_windows</IntermediateOutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <WarningLevel>5</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(TargetFramework)'=='Release|AnyCPU|net472'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\temp\bin\anycpu_release_v4_7_2</OutputPath>
    <IntermediateOutputPath>..\temp\obj\anycpu_release\helloworldexe_v4_7_2</IntermediateOutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <WarningLevel>5</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(TargetFramework)'=='Release|AnyCPU|net6.0'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\temp\bin\anycpu_release_net6_0</OutputPath>
    <IntermediateOutputPath>..\temp\obj\anycpu_release\helloworldexe_net6_0</IntermediateOutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <WarningLevel>5</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(TargetFramework)'=='Release|AnyCPU|net6.0-windows'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\temp\bin\anycpu_release_net6_0_windows</OutputPath>
    <IntermediateOutputPath>..\temp\obj\anycpu_release\helloworldexe_net6_0_windows</IntermediateOutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <WarningLevel>5</WarningLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup Condition="'$(TargetFramework)'=='net472'">
    <Reference Include="HelloWorldSwappedLib-net472">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\temp\bin\anycpu_release_v4_7_2\HelloWorldSwappedLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup Condition="'$(TargetFramework)'=='net6.0'">
    <Reference Include="HelloWorldSwappedLib-net6.0">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\temp\bin\anycpu_release_net6_0\HelloWorldSwappedLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup Condition="'$(TargetFramework)'=='net6.0-windows'">
    <Reference Include="HelloWorldSwappedLib-net6.0-windows">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\temp\bin\anycpu_release_net6_0_windows\HelloWorldSwappedLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HelloWorldLib\HelloWorldLib.vs2022.csproj">
      <Project>{854a5e48-aa9b-c244-2088-0c41cb5ab42b}</Project>
      <Name>HelloWorldLib</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup Condition="'$(TargetFramework)'=='net6.0' OR '$(TargetFramework)'=='net6.0-windows'">
    <PackageReference Include="Microsoft.Windows.Compatibility" Version="6.0.6" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
</Project>