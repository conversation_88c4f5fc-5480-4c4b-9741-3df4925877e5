# Generated by Sharpmake -- Do not edit !
ifndef config
  config=debug
endif

ifndef verbose
  SILENT = @
endif

ifeq ($(config),debug)
  CXX        = g++
  AR         = ar
  OBJDIR     = ../../obj/linux_debug/exe
  TARGETDIR  = ../../bin/linux_debug
  TARGET     = $(TARGETDIR)/exe
  DEFINES   += -D "CREATION_DATE=\"October 2020\"" -D "UTIL_DLL_IMPORT" -D "_DEBUG"
  INCLUDES  += -I../../../dll1 -I../../../header-only-lib -I../../../lib_group -I../../../static\ lib2
  CPPFLAGS  += -MMD -MP $(DEFINES) $(INCLUDES)
  CFLAGS    += $(CPPFLAGS) -g -Wall 
  CXXFLAGS  += $(CFLAGS) -fno-exceptions -fno-rtti 
  LDFLAGS   += -L../../bin/linux_debug -L../../lib/linux_debug/curl -L../../lib/linux_debug/static\ lib2 -Wl,-rpath='$$ORIGIN'
  LDLIBS    += -l:libcurl.a -l:libdll1.so -l:liblib_group.so -l:libstatic\ lib2.a -l:libuuid.so
  RESFLAGS  += $(DEFINES) $(INCLUDES)
  LDDEPS    += ../../bin/linux_debug/libdll1.so ../../bin/linux_debug/liblib_group.so ../../lib/linux_debug/static\ lib2/libstatic\ lib2.a ../../lib/linux_debug/static_lib1/libstatic_lib1.a
  LINKCMD    = $(CXX) -o $(TARGET) $(OBJECTS) $(LDFLAGS) $(RESOURCES) $(LDLIBS)
  PCH        = ../../../exe/stdafx.h
  PCHOUT     = $(OBJDIR)/stdafx.h
  GCH        = $(OBJDIR)/stdafx.h.gch
  PCHCMD     = -include $(PCHOUT)
  define PREBUILDCMDS
    mkdir -p $(TARGETDIR)/../../package
  endef
  define PRELINKCMDS
    
  endef
  define POSTBUILDCMDS
    cp $(TARGET) $(TARGETDIR)/../../package
  endef
  define POSTFILECMDS
  endef
endif

ifeq ($(config),release)
  CXX        = g++
  AR         = ar
  OBJDIR     = ../../obj/linux_release/exe
  TARGETDIR  = ../../bin/linux_release
  TARGET     = $(TARGETDIR)/exe
  DEFINES   += -D "CREATION_DATE=\"October 2020\"" -D "NDEBUG" -D "UTIL_DLL_IMPORT"
  INCLUDES  += -I../../../dll1 -I../../../header-only-lib -I../../../lib_group -I../../../static\ lib2
  CPPFLAGS  += -MMD -MP $(DEFINES) $(INCLUDES)
  CFLAGS    += $(CPPFLAGS) -g -O3 -Wall 
  CXXFLAGS  += $(CFLAGS) -fno-exceptions -fno-rtti 
  LDFLAGS   += -L../../bin/linux_release -L../../lib/linux_release/curl -L../../lib/linux_release/static\ lib2 -Wl,-rpath='$$ORIGIN'
  LDLIBS    += -l:libcurl.a -l:libdll1.so -l:liblib_group.so -l:libstatic\ lib2.a -l:libuuid.so
  RESFLAGS  += $(DEFINES) $(INCLUDES)
  LDDEPS    += ../../bin/linux_release/libdll1.so ../../bin/linux_release/liblib_group.so ../../lib/linux_release/static\ lib2/libstatic\ lib2.a ../../lib/linux_release/static_lib1/libstatic_lib1.a
  LINKCMD    = $(CXX) -o $(TARGET) $(OBJECTS) $(LDFLAGS) $(RESOURCES) $(LDLIBS)
  PCH        = ../../../exe/stdafx.h
  PCHOUT     = $(OBJDIR)/stdafx.h
  GCH        = $(OBJDIR)/stdafx.h.gch
  PCHCMD     = -include $(PCHOUT)
  define PREBUILDCMDS
    mkdir -p $(TARGETDIR)/../../package
  endef
  define PRELINKCMDS
    
  endef
  define POSTBUILDCMDS
    cp $(TARGET) $(TARGETDIR)/../../package
  endef
  define POSTFILECMDS
  endef
endif

ifeq ($(config),debug)
  OBJECTS += $(OBJDIR)/main.o
  OBJECTS += $(OBJDIR)/stdafx.o
endif

ifeq ($(config),release)
  OBJECTS += $(OBJDIR)/main.o
  OBJECTS += $(OBJDIR)/stdafx.o
endif

RESOURCES := \

SHELLTYPE := msdos
ifeq (,$(ComSpec)$(COMSPEC))
  SHELLTYPE := posix
endif
ifeq (/bin,$(findstring /bin,$(SHELL)))
  SHELLTYPE := posix
endif

.PHONY: clean prebuild prelink

all: $(TARGETDIR) $(OBJDIR) prebuild prelink $(TARGET)
	@:

$(TARGET): $(GCH) $(OBJECTS) $(LDDEPS) $(RESOURCES) | $(TARGETDIR)
	@echo Linking exe
	$(SILENT) $(LINKCMD)
	$(POSTBUILDCMDS)

$(TARGETDIR):
	@echo Creating $(TARGETDIR)
ifeq (posix,$(SHELLTYPE))
	$(SILENT) mkdir -p $(TARGETDIR)
else
	$(SILENT) if not exist $(subst /,\\,$(TARGETDIR)) mkdir $(subst /,\\,$(TARGETDIR))
endif

ifneq ($(OBJDIR),$(TARGETDIR))
$(OBJDIR):
	@echo Creating $(OBJDIR)
ifeq (posix,$(SHELLTYPE))
	$(SILENT) mkdir -p $(OBJDIR)
else
	$(SILENT) if not exist $(subst /,\\,$(OBJDIR)) mkdir $(subst /,\\,$(OBJDIR))
endif
endif

clean:
	@echo Cleaning exe
ifeq (posix,$(SHELLTYPE))
	$(SILENT) rm -f  $(TARGET)
	$(SILENT) rm -rf $(OBJDIR)
else
	$(SILENT) if exist $(subst /,\\,$(TARGET)) del $(subst /,\\,$(TARGET))
	$(SILENT) if exist $(subst /,\\,$(OBJDIR)) rmdir /s /q $(subst /,\\,$(OBJDIR))
endif

prebuild:
	$(PREBUILDCMDS)

prelink:
	$(PRELINKCMDS)

ifneq (,$(PCH))
$(GCH): $(PCH) | $(OBJDIR)
	@echo $(notdir $<)
	-$(SILENT) cp $< $(OBJDIR)
	$(SILENT) $(CXX) $(CXXFLAGS) -xc++-header -o "$@" -c "$<"
	$(SILENT) $(POSTFILECMDS)
endif

$(OBJDIR)/main.o: ../../../exe/main.cpp $(GCH) | $(OBJDIR)
	@echo $(notdir $<)
	$(SILENT) $(CXX) $(CXXFLAGS) $(PCHCMD) -o "$@" -c "$<"
	$(SILENT) $(POSTFILECMDS)

$(OBJDIR)/stdafx.o: ../../../exe/stdafx.cpp $(GCH) | $(OBJDIR)
	@echo $(notdir $<)
	$(SILENT) $(CXX) $(CXXFLAGS) $(PCHCMD) -o "$@" -c "$<"
	$(SILENT) $(POSTFILECMDS)

-include $(OBJECTS:%.o=%.d)
-include $(GCH:%.gch=%.d)
